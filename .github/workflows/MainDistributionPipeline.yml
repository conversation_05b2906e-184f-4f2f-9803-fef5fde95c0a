#
# This workflow calls the main distribution pipeline from DuckDB to build, test and (optionally) release the extension
#
name: Main Extension Distribution Pipeline
on:
  push:
  pull_request:
  workflow_dispatch:

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}-${{ github.head_ref || '' }}-${{ github.base_ref || '' }}-${{ github.ref != 'refs/heads/main' || github.sha }}
  cancel-in-progress: true

jobs:
  duckdb-stable-build:
    name: Build extension binaries
    uses: duckdb/extension-ci-tools/.github/workflows/_extension_distribution.yml@v1.3.1
    with:
      duckdb_version: v1.3.1
      ci_tools_version: v1.3.1
      extension_name: rusty_quack
      extra_toolchains: rust;python3
      exclude_archs: 'wasm_mvp;wasm_eh;wasm_threads;linux_amd64_musl'
