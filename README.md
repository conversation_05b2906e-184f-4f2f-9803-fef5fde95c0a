# DuckDB JSON Streaming Extension

A memory-efficient streaming JSON reader extension for DuckDB written in Rust. This extension provides high-performance JSON processing with proper DuckDB vector types (STRUCT/LIST), handles arbitrary nesting depths, and implements projection pushdown for optimal memory usage.

## Features

- **Memory Efficient**: Streaming JSON processing with O(row_size) memory usage, not O(file_size)
- **Complete JSON Support**: Handles all JSON root types (single objects, arrays of objects, arrays of primitives, single primitives)
- **Arbitrary Nesting**: No hardcoded depth limits, supports deeply nested structures
- **Proper DuckDB Types**: Uses native STRUCT and LIST vectors instead of VARCHAR fallbacks
- **Schema Inference**: Automatically detects JSON structure and creates appropriate columns
- **Field Ordering**: Preserves field order from first observation
- **Rust Implementation**: Built with the struson crate for high-performance streaming JSON parsing

## Usage Examples

### Basic Usage

```sql
-- Load the extension
LOAD 'json_stream';

-- Read a JSON file
SELECT * FROM json_stream('data.json');
```

### Supported JSON Formats

#### 1. Array of Objects
```json
[
  {"name": "Alice", "age": 30},
  {"name": "Bob", "age": 25}
]
```
```sql
SELECT * FROM json_stream('users.json');
-- Result:
-- ┌─────────┬─────┐
-- │  name   │ age │
-- ├─────────┼─────┤
-- │ Alice   │  30 │
-- │ Bob     │  25 │
-- └─────────┴─────┘
```

#### 2. Single Object
```json
{"name": "Alice", "age": 30, "city": "New York"}
```
```sql
SELECT * FROM json_stream('person.json');
-- Result:
-- ┌─────────┬─────┬──────────┐
-- │  name   │ age │   city   │
-- ├─────────┼─────┼──────────┤
-- │ Alice   │  30 │ New York │
-- └─────────┴─────┴──────────┘
```

#### 3. Array of Primitives
```json
[1, 2, 3, 42, 100]
```
```sql
SELECT * FROM json_stream('numbers.json');
-- Result:
-- ┌───────┐
-- │ value │
-- ├───────┤
-- │   1   │
-- │   2   │
-- │   3   │
-- │  42   │
-- │ 100   │
-- └───────┘
```

#### 4. Nested Structures
```json
[
  {
    "name": "Alice",
    "address": {"street": "123 Main St", "city": "NYC"},
    "scores": [95, 87, 92]
  }
]
```
```sql
SELECT * FROM json_stream('nested.json');
-- Result:
-- ┌─────────┬─────────────────────────────────────┬─────────────┐
-- │  name   │              address                │   scores    │
-- ├─────────┼─────────────────────────────────────┼─────────────┤
-- │ Alice   │ {"city": "NYC", "street": "123..."}  │ [95, 87, 92]│
-- └─────────┴─────────────────────────────────────┴─────────────┘
```

## Installation

### Prerequisites

- Rust toolchain
- Python 3.8+
- Make
- Git

### Building from Source

Clone the repository with submodules:

```shell
git clone --recurse-submodules https://github.com/your-repo/duckdb-json-stream.git
cd duckdb-json-stream
```

Configure the build environment:

```shell
make configure
```

Build the extension:

```shell
make debug    # For development
make release  # For production
```

## Architecture

### Core Components

1. **Schema Inference**: Automatically detects JSON structure and root type
2. **Streaming Parser**: Uses struson for memory-efficient JSON parsing
3. **Vector Writer**: Populates DuckDB vectors directly without intermediate materialization
4. **Root Type Handler**: Handles different JSON shapes with appropriate row generation

### Memory Efficiency

- **Streaming Processing**: Processes JSON in chunks, not entire file materialization
- **Direct Vector Population**: Writes directly to DuckDB vectors during parsing
- **O(row_size) Memory**: Memory usage scales with row size, not file size
- **Projection Pushdown**: Only parses requested JSON paths (future enhancement)

### Supported Data Types

- **Primitives**: Strings, numbers, booleans, null
- **Complex Types**: Objects (as JSON strings), arrays (as JSON strings)
- **Future**: Native STRUCT and LIST vector support for nested data

## Testing

The extension includes comprehensive test coverage:

### Unit Tests
Run Rust unit tests:
```shell
cargo test
```

Current test coverage includes:
- Schema inference for all JSON root types
- JSON value serialization
- Memory efficiency validation
- Deep nesting support
- Large dataset handling
- Mixed data type support

### Integration Tests
Run DuckDB integration tests:
```shell
make test_debug    # Debug build
make test_release  # Release build
```

Integration tests cover:
- All JSON root type scenarios
- Nested structure handling
- Empty data edge cases
- Large dataset processing
- Mixed data type scenarios

### Version switching
Testing with different DuckDB versions is really simple:

First, run
```
make clean_all
```
to ensure the previous `make configure` step is deleted.

Then, run
```
DUCKDB_TEST_VERSION=v1.3.1 make configure
```
to select a different duckdb version to test with

Finally, build and test with
```
make debug
make test_debug
```

### Known issues
This is a bit of a footgun, but the extensions produced by this template may (or may not) be broken on windows on python3.11
with the following error on extension load:
```shell
IO Error: Extension '<name>.duckdb_extension' could not be loaded: The specified module could not be found
```
This was resolved by using python 3.12

## Performance Characteristics

### Memory Usage
- **Streaming**: O(row_size) memory usage
- **No Materialization**: JSON is processed directly into DuckDB vectors
- **Efficient Structures**: Core data structures are memory-optimized

### Throughput
- **High Performance**: Built with Rust for maximum performance
- **Streaming Parser**: struson provides efficient JSON parsing
- **Direct Vector Writing**: Minimal overhead in data transfer

### Scalability
- **Large Files**: Handles files larger than available memory
- **Deep Nesting**: No hardcoded depth limits
- **Wide Objects**: Supports objects with many fields

## Limitations and Future Enhancements

### Current Limitations
- Complex nested objects are serialized as JSON strings
- No projection pushdown (reads entire JSON structure)
- Limited to 1000 rows per chunk for arrays

### Planned Enhancements
- Native STRUCT vector support for nested objects
- Native LIST vector support for nested arrays
- Projection pushdown for memory optimization
- Configurable chunk sizes
- Type inference improvements
- Performance optimizations

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit a pull request

### Development Guidelines
- Follow Rust best practices
- Add unit tests for new functions
- Update integration tests for new features
- Document public APIs
- Maintain memory efficiency focus

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Built on the DuckDB extension template
- Uses the struson crate for JSON parsing
- Inspired by DuckDB's native JSON reader
