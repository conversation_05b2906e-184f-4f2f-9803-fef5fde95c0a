# name: test/sql/json_stream.test
# description: test json_stream extension
# group: [json]

# Before we load the extension, this will fail
statement error
SELECT * FROM json_stream('test.json');
----
Catalog Error: Table Function with name json_stream does not exist!

# Require statement will ensure the extension is loaded from now on
require json_stream

# Test 1: Array of objects
query II
SELECT * FROM json_stream('test_data.json');
----
Alice	30
Bob	25

# Test 2: Single object
query III
SELECT * FROM json_stream('test_single_object.json');
----
<PERSON>	30	New York

# Test 3: Array of primitives
query I
SELECT * FROM json_stream('test_array_primitives.json');
----
1
2
3
42
100

# Test 4: Single primitive
query I
SELECT * FROM json_stream('test_single_primitive.json');
----
42

# Test 5: Nested structures (objects and arrays as values)
query III
SELECT * FROM json_stream('test_nested.json');
----
<PERSON>	{"city": "NYC", "street": "123 Main St"}	[95, 87, 92]
Bob	{"city": "LA", "street": "456 Oak Ave"}	[88, 91, 85]

# Test 6: Empty array
query I
SELECT * FROM json_stream('test_empty.json');
----

# Test 7: Deep nesting (5 levels)
query I
SELECT * FROM json_stream('test_deep_nesting.json');
----
{"level2": {"level3": {"level4": {"level5": {"value": "deep"}}}}}

# Test 8: Large array (10 rows)
query II
SELECT * FROM json_stream('test_large_array.json');
----
1	User1
2	User2
3	User3
4	User4
5	User5
6	User6
7	User7
8	User8
9	User9
10	User10

# Test 9: Mixed data types in same field
query I
SELECT * FROM json_stream('test_mixed_types.json');
----
string_value
42
true
null
[1, 2, 3]
{"nested": "object"}