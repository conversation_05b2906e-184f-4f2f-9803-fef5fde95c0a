# name: test/sql/json_stream.test
# description: test json_stream extension
# group: [json]

# Before we load the extension, this will fail
statement error
SELECT * FROM json_stream('test.json');
----
Catalog Error: Table Function with name json_stream does not exist!

# Require statement will ensure the extension is loaded from now on
require json_stream

# Basic test with simple JSON object
query I
SELECT * FROM json_stream('{"name": "Alice", "age": 30}');
----