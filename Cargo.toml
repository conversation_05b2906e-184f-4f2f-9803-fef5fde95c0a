[package]
name = "json_stream"
version = "0.1.0"
edition = "2021"

[lib]
crate-type = ["cdylib"]

[profile.release]
lto = true
strip = true

[[example]]
# crate-type can't be (at the moment) be overriden for specific targets
# src/wasm_lib.rs forwards to src/lib.rs so that we can change from cdylib
# (that is needed while compiling natively) to staticlib (needed since the
# actual linking will be done via emcc
name = "json_stream"
path = "src/wasm_lib.rs"
crate-type = ["staticlib"]

[dependencies]
duckdb = { version = "1.3.1", features = ["vtab-loadable"] }
duckdb-loadable-macros = "0.1.5"
libduckdb-sys = { version = "1.3.1", features = ["loadable-extension"] }
struson = "0.5"

[dev-dependencies]
tempfile = "3.0"
