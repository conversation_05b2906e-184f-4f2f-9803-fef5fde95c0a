{"linux": {"include": [{"duckdb_arch": "linux_amd64", "runner": "ubuntu-24.04", "vcpkg_target_triplet": "x64-linux-release", "vcpkg_host_triplet": "x64-linux-release"}, {"duckdb_arch": "linux_arm64", "runner": "ubuntu-24.04-arm", "vcpkg_target_triplet": "arm64-linux-release", "vcpkg_host_triplet": "arm64-linux-linux-release"}, {"duckdb_arch": "linux_amd64_musl", "runner": "ubuntu-24.04", "vcpkg_target_triplet": "x64-linux-release", "vcpkg_host_triplet": "x64-linux-release"}]}, "osx": {"include": [{"duckdb_arch": "osx_amd64", "osx_build_arch": "x86_64", "vcpkg_target_triplet": "x64-osx-release", "vcpkg_host_triplet": "x64-osx-release"}, {"duckdb_arch": "osx_arm64", "osx_build_arch": "arm64", "vcpkg_target_triplet": "arm64-osx-release", "vcpkg_host_triplet": "arm64-osx-release"}]}, "windows": {"include": [{"duckdb_arch": "windows_amd64", "vcpkg_target_triplet": "x64-windows-static-md-release", "vcpkg_host_triplet": "x64-windows-static-md-release"}, {"duckdb_arch": "windows_amd64_mingw", "vcpkg_target_triplet": "x64-mingw-static", "vcpkg_host_triplet": "x64-mingw-static"}]}, "wasm": {"include": [{"duckdb_arch": "wasm_mvp", "vcpkg_target_triplet": "wasm32-emscripten", "vcpkg_host_triplet": "x64-linux"}, {"duckdb_arch": "wasm_eh", "vcpkg_target_triplet": "wasm32-emscripten", "vcpkg_host_triplet": "x64-linux"}, {"duckdb_arch": "wasm_threads", "vcpkg_target_triplet": "wasm32-emscripten", "vcpkg_host_triplet": "x64-linux"}]}}