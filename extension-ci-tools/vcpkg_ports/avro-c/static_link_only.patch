diff --git a/lang/c/src/CMakeLists.txt b/lang/c/src/CMakeLists.txt
index c1761c8de..25ca3b1a3 100644
--- a/lang/c/src/CMakeLists.txt
+++ b/lang/c/src/CMakeLists.txt
@@ -88,16 +88,6 @@ add_library(avro-static STATIC ${AVRO_SRC})
 target_link_libraries(avro-static ${JANSSON_LIBRARIES} ${CODEC_LIBRARIES} ${THREADS_LIBRARIES})
 set_target_properties(avro-static PROPERTIES OUTPUT_NAME avro)
 
-if (NOT WIN32)
-# TODO: Create Windows DLLs. See https://www.cmake.org/Wiki/BuildingWinDLL
-add_library(avro-shared SHARED ${AVRO_SRC})
-target_link_libraries(avro-shared ${JANSSON_LIBRARIES} ${CODEC_LIBRARIES} ${THREADS_LIBRARIES})
-set_target_properties(avro-shared PROPERTIES
-        OUTPUT_NAME avro
-        VERSION ${LIBAVRO_DOT_VERSION}
-        SOVERSION ${LIBAVRO_SOVERSION})
-endif(NOT WIN32)
-
 install(FILES
         ${CMAKE_CURRENT_SOURCE_DIR}/avro.h
         DESTINATION include)
@@ -108,19 +98,11 @@ install(DIRECTORY
 
 include(GNUInstallDirs)
 
-if (WIN32)
 install(TARGETS avro-static
 	RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
         LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
         ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
        )
-else(WIN32)
-install(TARGETS avro-static avro-shared
-	RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
-	LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
-        ARCHIVE DESTINATION ${CMAKE_INSTALL_LIBDIR}
-       )
-endif(WIN32)
 
 # Install pkg-config file
 
