diff --git a/lang/c/src/avro/io.h b/lang/c/src/avro/io.h
index ffbb68dc5..06253fe48 100644
--- a/lang/c/src/avro/io.h
+++ b/lang/c/src/avro/io.h
@@ -52,6 +52,10 @@ avro_reader_memory_set_source(avro_reader_t reader, const char *buf, int64_t len
 void
 avro_writer_memory_set_dest(avro_writer_t writer, const char *buf, int64_t len);
 
+void
+avro_writer_memory_set_dest_with_offset(avro_writer_t writer, const char *buf, int64_t len, int64_t offset);
+
+
 int avro_read(avro_reader_t reader, void *buf, int64_t len);
 int avro_skip(avro_reader_t reader, int64_t len);
 int avro_write(avro_writer_t writer, void *buf, int64_t len);
@@ -60,6 +64,8 @@ void avro_reader_reset(avro_reader_t reader);
 
 void avro_writer_reset(avro_writer_t writer);
 int64_t avro_writer_tell(avro_writer_t writer);
+const char *avro_writer_buf(avro_writer_t writer);
+
 void avro_writer_flush(avro_writer_t writer);
 
 void avro_writer_dump(avro_writer_t writer, FILE * fp);
@@ -109,6 +115,7 @@ int avro_file_writer_create_with_codec(const char *path,
 int avro_file_writer_create_with_codec_fp(FILE *fp, const char *path, int should_close,
 				avro_schema_t schema, avro_file_writer_t * writer,
 				const char *codec, size_t block_size);
+int avro_file_writer_create_from_writers(avro_writer_t writer_in, avro_writer_t datum_writer_in, avro_schema_t schema, avro_file_writer_t * writer);
 int avro_file_writer_open(const char *path, avro_file_writer_t * writer);
 int avro_file_writer_open_bs(const char *path, avro_file_writer_t * writer, size_t block_size);
 int avro_file_reader(const char *path, avro_file_reader_t * reader);
diff --git a/lang/c/src/datafile.c b/lang/c/src/datafile.c
index c9d4dfeb6..45d8084ee 100644
--- a/lang/c/src/datafile.c
+++ b/lang/c/src/datafile.c
@@ -238,6 +238,43 @@ int avro_file_writer_create_with_codec_fp(FILE *fp, const char *path, int should
 	return 0;
 }
 
+int avro_file_writer_create_from_writers(avro_writer_t writer_in, avro_writer_t datum_writer_in, avro_schema_t schema, avro_file_writer_t * writer)
+{
+	avro_file_writer_t w;
+	int rval;
+	check_param(EINVAL, is_avro_schema(schema), "schema");
+	check_param(EINVAL, writer, "writer");
+
+	w = (avro_file_writer_t) avro_new(struct avro_file_writer_t_);
+	if (!w) {
+		avro_set_error("Cannot allocate new file writer");
+		return ENOMEM;
+	}
+	w->block_count = 0;
+	w->codec = (avro_codec_t) avro_new(struct avro_codec_t_);
+	if (!w->codec) {
+		avro_set_error("Cannot allocate new codec");
+		avro_freet(struct avro_file_writer_t_, w);
+		return ENOMEM;
+	}
+	rval = avro_codec(w->codec, NULL);
+	if (rval) {
+		avro_codec_reset(w->codec);
+		avro_freet(struct avro_codec_t_, w->codec);
+		avro_freet(struct avro_file_writer_t_, w);
+		return rval;
+	}
+	w->writer = writer_in;
+	*writer = w;
+
+	w->datum_buffer_size = 0;
+	w->datum_buffer = NULL;
+	w->datum_writer = datum_writer_in;
+
+	w->writers_schema = avro_schema_incref(schema);
+	return write_header(w);
+}
+
 static int file_read_header(avro_reader_t reader,
 			    avro_schema_t * writers_schema, avro_codec_t codec,
 			    char *sync, int synclen)
@@ -570,7 +607,7 @@ static int file_write_block(avro_file_writer_t w)
 		check_prefix(rval, enc->write_long(w->writer, w->block_count),
 			     "Cannot write file block count: ");
 		/* Encode the block */
-		check_prefix(rval, avro_codec_encode(w->codec, w->datum_buffer, w->block_size),
+		check_prefix(rval, avro_codec_encode(w->codec, (void *)avro_writer_buf(w->datum_writer), w->block_size),
 			     "Cannot encode file block: ");
 		/* Write the block length */
 		check_prefix(rval, enc->write_long(w->writer, w->codec->used_size),
diff --git a/lang/c/src/io.c b/lang/c/src/io.c
index c1e2f5dc9..5fec10b0d 100644
--- a/lang/c/src/io.c
+++ b/lang/c/src/io.c
@@ -183,6 +183,17 @@ avro_writer_memory_set_dest(avro_writer_t writer, const char *buf, int64_t len)
 	}
 }
 
+void
+avro_writer_memory_set_dest_with_offset(avro_writer_t writer, const char *buf, int64_t len, int64_t offset)
+{
+	if (is_memory_io(writer)) {
+		struct _avro_writer_memory_t *mem_writer = avro_writer_to_memory(writer);
+		mem_writer->buf = buf;
+		mem_writer->len = len;
+		mem_writer->written = offset;
+	}
+}
+
 static int
 avro_read_memory(struct _avro_reader_memory_t *reader, void *buf, int64_t len)
 {
@@ -388,6 +399,14 @@ int64_t avro_writer_tell(avro_writer_t writer)
 	return EINVAL;
 }
 
+const char *avro_writer_buf(avro_writer_t writer)
+{
+	if (is_memory_io(writer)) {
+		return avro_writer_to_memory(writer)->buf;
+	}
+	return NULL;
+}
+
 void avro_writer_flush(avro_writer_t writer)
 {
 	if (is_file_io(writer)) {
