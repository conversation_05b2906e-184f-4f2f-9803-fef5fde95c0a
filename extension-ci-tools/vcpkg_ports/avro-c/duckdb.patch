diff --git a/lang/c/src/avro/io.h b/lang/c/src/avro/io.h
index ffbb68dc5..06af2e44d 100644
--- a/lang/c/src/avro/io.h
+++ b/lang/c/src/avro/io.h
@@ -114,6 +114,10 @@ int avro_file_writer_open_bs(const char *path, avro_file_writer_t * writer, size
 int avro_file_reader(const char *path, avro_file_reader_t * reader);
 int avro_file_reader_fp(FILE *fp, const char *path, int should_close,
 			avro_file_reader_t * reader);
+int avro_reader_reader(avro_reader_t reader_in,
+			avro_file_reader_t * reader);
+int avro_reader_is_memory(avro_reader_t reader);
+int avro_reader_memory_is_depleted(avro_reader_t reader);
 
 avro_schema_t
 avro_file_reader_get_writer_schema(avro_file_reader_t reader);
diff --git a/lang/c/src/datafile.c b/lang/c/src/datafile.c
index c9d4dfeb6..16f73d821 100644
--- a/lang/c/src/datafile.c
+++ b/lang/c/src/datafile.c
@@ -541,6 +541,77 @@ int avro_file_reader_fp(FILE *fp, const char *path, int should_close,
 	return 0;
 }
 
+int avro_reader_reader(avro_reader_t reader_in,	avro_file_reader_t * reader)
+{
+	if (!avro_reader_is_memory(reader_in)) {
+		avro_set_error("Cannot create a file_reader from a non-memory reader");
+		return EINVAL;
+	}
+
+	const char* path = "";
+	int rval;
+	avro_file_reader_t r = (avro_file_reader_t) avro_new(struct avro_file_reader_t_);
+	if (!r) {
+		avro_set_error("Cannot allocate file reader for %s", path);
+		return ENOMEM;
+	}
+
+	r->reader = reader_in;
+	if (!r->reader) {
+		avro_set_error("Cannot allocate reader for file %s", path);
+		avro_freet(struct avro_file_reader_t_, r);
+		return ENOMEM;
+	}
+	r->block_reader = avro_reader_memory(0, 0);
+	if (!r->block_reader) {
+		avro_set_error("Cannot allocate block reader for file %s", path);
+		avro_reader_free(r->reader);
+		avro_freet(struct avro_file_reader_t_, r);
+		return ENOMEM;
+	}
+
+	r->codec = (avro_codec_t) avro_new(struct avro_codec_t_);
+	if (!r->codec) {
+		avro_set_error("Could not allocate codec for file %s", path);
+		avro_reader_free(r->reader);
+		avro_freet(struct avro_file_reader_t_, r);
+		return ENOMEM;
+	}
+	avro_codec(r->codec, NULL);
+
+	rval = file_read_header(r->reader, &r->writers_schema, r->codec,
+				r->sync, sizeof(r->sync));
+	if (rval) {
+		avro_reader_free(r->reader);
+		avro_codec_reset(r->codec);
+		avro_freet(struct avro_codec_t_, r->codec);
+		avro_freet(struct avro_file_reader_t_, r);
+		return rval;
+	}
+
+	r->current_blockdata = NULL;
+	r->current_blocklen = 0;
+
+	if (avro_reader_memory_is_depleted(r->reader)) {
+		rval = EOF;
+	} else {
+		rval = file_read_block_count(r);
+	}
+
+	if (rval == EOF) {
+		r->blocks_total = 0;
+	} else if (rval) {
+		avro_reader_free(r->reader);
+		avro_codec_reset(r->codec);
+		avro_freet(struct avro_codec_t_, r->codec);
+		avro_freet(struct avro_file_reader_t_, r);
+		return rval;
+	}
+
+	*reader = r;
+	return 0;
+}
+
 int avro_file_reader(const char *path, avro_file_reader_t * reader)
 {
 	FILE *fp;
diff --git a/lang/c/src/io.c b/lang/c/src/io.c
index c1e2f5dc9..0b50c2ea0 100644
--- a/lang/c/src/io.c
+++ b/lang/c/src/io.c
@@ -157,6 +157,15 @@ avro_reader_memory_set_source(avro_reader_t reader, const char *buf, int64_t len
 	}
 }
 
+int avro_reader_memory_is_depleted(avro_reader_t reader)
+{
+	if (!is_memory_io(reader)) {
+		return 0;
+	}
+	struct _avro_reader_memory_t *mem_reader = avro_reader_to_memory(reader);
+	return mem_reader->read == mem_reader->len;
+}
+
 avro_writer_t avro_writer_memory(const char *buf, int64_t len)
 {
 	struct _avro_writer_memory_t *mem_writer =
@@ -445,3 +454,7 @@ int avro_reader_is_eof(avro_reader_t reader)
 	}
 	return 0;
 }
+
+int avro_reader_is_memory(avro_reader_t reader) {
+	return is_memory_io(reader);
+}
