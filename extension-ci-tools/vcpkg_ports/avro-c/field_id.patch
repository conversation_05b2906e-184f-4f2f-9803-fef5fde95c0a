diff --git a/lang/c/src/avro/schema.h b/lang/c/src/avro/schema.h
index 51d456155..919199fa1 100644
--- a/lang/c/src/avro/schema.h
+++ b/lang/c/src/avro/schema.h
@@ -43,6 +43,7 @@ avro_schema_t avro_schema_null(void);
 avro_schema_t avro_schema_record(const char *name, const char *space);
 avro_schema_t avro_schema_record_field_get(const avro_schema_t
 					   record, const char *field_name);
+int32_t avro_schema_record_field_id(const avro_schema_t record, int index);
 const char *avro_schema_record_field_name(const avro_schema_t schema, int index);
 int avro_schema_record_field_get_index(const avro_schema_t schema,
 				       const char *field_name);
@@ -50,7 +51,8 @@ avro_schema_t avro_schema_record_field_get_by_index
 (const avro_schema_t record, int index);
 int avro_schema_record_field_append(const avro_schema_t record,
 				    const char *field_name,
-				    const avro_schema_t type);
+				    const avro_schema_t type,
+				    int32_t field_id);
 size_t avro_schema_record_size(const avro_schema_t record);
 
 avro_schema_t avro_schema_enum(const char *name);
@@ -68,11 +70,15 @@ avro_schema_t avro_schema_fixed_ns(const char *name, const char *space,
 				   const int64_t len);
 int64_t avro_schema_fixed_size(const avro_schema_t fixed);
 
-avro_schema_t avro_schema_map(const avro_schema_t values);
+avro_schema_t avro_schema_map(const avro_schema_t values, int32_t key_id, int32_t value_id);
 avro_schema_t avro_schema_map_values(avro_schema_t map);
+int32_t avro_schema_map_key_id(avro_schema_t map);
+int32_t avro_schema_map_value_id(avro_schema_t map);
 
-avro_schema_t avro_schema_array(const avro_schema_t items);
+avro_schema_t avro_schema_array(const avro_schema_t items, int32_t element_id, const char *logical_type);
 avro_schema_t avro_schema_array_items(avro_schema_t array);
+int32_t avro_schema_array_element_id(avro_schema_t array);
+int avro_schema_array_is_map(avro_schema_t array);
 
 avro_schema_t avro_schema_union(void);
 size_t avro_schema_union_size(const avro_schema_t union_schema);
diff --git a/lang/c/src/datafile.c b/lang/c/src/datafile.c
index c9d4dfeb6..ce7f41740 100644
--- a/lang/c/src/datafile.c
+++ b/lang/c/src/datafile.c
@@ -261,7 +261,7 @@ static int file_read_header(avro_reader_t reader,
 	}
 
 	meta_values_schema = avro_schema_bytes();
-	meta_schema = avro_schema_map(meta_values_schema);
+	meta_schema = avro_schema_map(meta_values_schema, INT32_MAX, INT32_MAX);
 	meta_iface = avro_generic_class_from_schema(meta_schema);
 	if (meta_iface == NULL) {
 		return EILSEQ;
diff --git a/lang/c/src/schema.c b/lang/c/src/schema.c
index 7b389002b..61dd67eab 100644
--- a/lang/c/src/schema.c
+++ b/lang/c/src/schema.c
@@ -182,6 +182,9 @@ static void avro_schema_free(avro_schema_t schema)
 				struct avro_array_schema_t *array;
 				array = avro_schema_to_array(schema);
 				avro_schema_decref(array->items);
+				if (array->logical_type) {
+					avro_str_free((char *) array->logical_type);
+				}
 				avro_freet(struct avro_array_schema_t, array);
 			}
 			break;
@@ -443,7 +446,7 @@ avro_schema_t avro_schema_union_branch_by_name
 	return avro_schema_union_branch(unionp, val.branch_index);
 }
 
-avro_schema_t avro_schema_array(const avro_schema_t items)
+avro_schema_t avro_schema_array(const avro_schema_t items, int32_t element_id, const char *logical_type)
 {
 	struct avro_array_schema_t *array =
 	    (struct avro_array_schema_t *) avro_new(struct avro_array_schema_t);
@@ -452,6 +455,11 @@ avro_schema_t avro_schema_array(const avro_schema_t items)
 		return NULL;
 	}
 	array->items = avro_schema_incref(items);
+	array->logical_type = NULL;
+	if (logical_type) {
+		array->logical_type = avro_strdup(logical_type);
+	}
+	array->element_id = element_id;
 	avro_schema_init(&array->obj, AVRO_ARRAY);
 	return &array->obj;
 }
@@ -461,7 +469,21 @@ avro_schema_t avro_schema_array_items(avro_schema_t array)
 	return avro_schema_to_array(array)->items;
 }
 
-avro_schema_t avro_schema_map(const avro_schema_t values)
+int32_t avro_schema_array_element_id(avro_schema_t array)
+{
+	return avro_schema_to_array(array)->element_id;
+}
+
+int avro_schema_array_is_map(avro_schema_t array)
+{
+	const char *logical_type = avro_schema_to_array(array)->logical_type;
+	if (!logical_type) {
+		return 0;
+	}
+	return strcmp(logical_type, "map") == 0;
+}
+
+avro_schema_t avro_schema_map(const avro_schema_t values, int32_t key_id, int32_t value_id)
 {
 	struct avro_map_schema_t *map =
 	    (struct avro_map_schema_t *) avro_new(struct avro_map_schema_t);
@@ -470,6 +492,8 @@ avro_schema_t avro_schema_map(const avro_schema_t values)
 		return NULL;
 	}
 	map->values = avro_schema_incref(values);
+	map->key_id = key_id;
+	map->value_id = value_id;
 	avro_schema_init(&map->obj, AVRO_MAP);
 	return &map->obj;
 }
@@ -479,6 +503,16 @@ avro_schema_t avro_schema_map_values(avro_schema_t map)
 	return avro_schema_to_map(map)->values;
 }
 
+int32_t avro_schema_map_key_id(avro_schema_t map)
+{
+	return avro_schema_to_map(map)->key_id;
+}
+
+int32_t avro_schema_map_value_id(avro_schema_t map)
+{
+	return avro_schema_to_map(map)->value_id;
+}
+
 avro_schema_t avro_schema_enum(const char *name)
 {
 	return avro_schema_enum_ns(name, NULL);
@@ -593,7 +627,8 @@ avro_schema_enum_number_of_symbols(const avro_schema_t enum_schema)
 int
 avro_schema_record_field_append(const avro_schema_t record_schema,
 				const char *field_name,
-				const avro_schema_t field_schema)
+				const avro_schema_t field_schema,
+				int32_t field_id)
 {
 	check_param(EINVAL, is_avro_schema(record_schema), "record schema");
 	check_param(EINVAL, is_avro_record(record_schema), "record schema");
@@ -619,6 +654,7 @@ avro_schema_record_field_append(const avro_schema_t record_schema,
 	new_field->index = record->fields->num_entries;
 	new_field->name = avro_strdup(field_name);
 	new_field->type = avro_schema_incref(field_schema);
+	new_field->field_id = field_id;
 	st_insert(record->fields, record->fields->num_entries,
 		  (st_data_t) new_field);
 	st_insert(record->fields_byname, (st_data_t) new_field->name,
@@ -694,6 +730,16 @@ avro_schema_t avro_schema_record_field_get(const avro_schema_t
 	return val.field->type;
 }
 
+int32_t avro_schema_record_field_id(const avro_schema_t schema, int index)
+{
+	union {
+		st_data_t data;
+		struct avro_record_field_t *field;
+	} val;
+	st_lookup(avro_schema_to_record(schema)->fields, index, &val.data);
+	return val.field->field_id;
+}
+
 int avro_schema_record_field_get_index(const avro_schema_t schema,
 				       const char *field_name)
 {
@@ -872,6 +918,15 @@ avro_type_from_json_t(json_t *json, avro_type_t *type,
 	return 0;
 }
 
+static int
+field_id_from_json_t(json_t *json, int32_t *field_id)
+{
+	if (json != NULL && json_is_integer(json)) {
+		*field_id = json_integer_value(json);
+	}
+	return 0;
+}
+
 static int
 avro_schema_from_json_t(json_t *json, avro_schema_t *schema,
 			st_table *named_schemas, const char *parent_namespace)
@@ -979,7 +1034,9 @@ avro_schema_from_json_t(json_t *json, avro_schema_t *schema,
 				    json_array_get(json_fields, i);
 				json_t *json_field_name;
 				json_t *json_field_type;
+				json_t *json_field_field_id;
 				avro_schema_t json_field_type_schema;
+				int32_t field_id = INT32_MAX;
 				int field_rval;
 
 				if (!json_is_object(json_field)) {
@@ -1001,6 +1058,8 @@ avro_schema_from_json_t(json_t *json, avro_schema_t *schema,
 					avro_schema_decref(*schema);
 					return EINVAL;
 				}
+				json_field_field_id =
+				    json_object_get(json_field, "field-id");
 				field_rval =
 				    avro_schema_from_json_t(json_field_type,
 							    &json_field_type_schema,
@@ -1010,11 +1069,18 @@ avro_schema_from_json_t(json_t *json, avro_schema_t *schema,
 					avro_schema_decref(*schema);
 					return field_rval;
 				}
+				field_rval =
+					field_id_from_json_t(json_field_field_id, &field_id);
+				if (field_rval) {
+					avro_schema_decref(*schema);
+					return field_rval;
+				}
 				field_rval =
 				    avro_schema_record_field_append(*schema,
 								    json_string_value
 								    (json_field_name),
-								    json_field_type_schema);
+								    json_field_type_schema,
+								    field_id);
 				avro_schema_decref(json_field_type_schema);
 				if (field_rval != 0) {
 					avro_schema_decref(*schema);
@@ -1100,7 +1166,11 @@ avro_schema_from_json_t(json_t *json, avro_schema_t *schema,
 		{
 			int items_rval;
 			json_t *json_items = json_object_get(json, "items");
+			json_t *json_element_id = json_object_get(json, "element-id");
+			json_t *json_logical_type = json_object_get(json, "logicalType");
 			avro_schema_t items_schema;
+			const char *logical_type = NULL;
+			int32_t element_id = INT32_MAX;
 			if (!json_items) {
 				avro_set_error("Array type must have \"items\"");
 				return EINVAL;
@@ -1111,7 +1181,15 @@ avro_schema_from_json_t(json_t *json, avro_schema_t *schema,
 			if (items_rval) {
 				return items_rval;
 			}
-			*schema = avro_schema_array(items_schema);
+			if (json_logical_type) {
+				logical_type = json_string_value(json_logical_type);
+			}
+			items_rval =
+			    field_id_from_json_t(json_element_id, &element_id);
+			if (items_rval) {
+				return items_rval;
+			}
+			*schema = avro_schema_array(items_schema, element_id, logical_type);
 			avro_schema_decref(items_schema);
 		}
 		break;
@@ -1120,7 +1198,11 @@ avro_schema_from_json_t(json_t *json, avro_schema_t *schema,
 		{
 			int values_rval;
 			json_t *json_values = json_object_get(json, "values");
+			json_t *json_key_id = json_object_get(json, "key-id");
+			json_t *json_value_id = json_object_get(json, "value-id");
 			avro_schema_t values_schema;
+			int32_t key_id = INT32_MAX;
+			int32_t value_id = INT32_MAX;
 
 			if (!json_values) {
 				avro_set_error("Map type must have \"values\"");
@@ -1132,7 +1214,15 @@ avro_schema_from_json_t(json_t *json, avro_schema_t *schema,
 			if (values_rval) {
 				return values_rval;
 			}
-			*schema = avro_schema_map(values_schema);
+			values_rval = field_id_from_json_t(json_key_id, &key_id);
+			if (values_rval) {
+				return values_rval;
+			}
+			values_rval = field_id_from_json_t(json_value_id, &value_id);
+			if (values_rval) {
+				return values_rval;
+			}
+			*schema = avro_schema_map(values_schema, key_id, value_id);
 			avro_schema_decref(values_schema);
 		}
 		break;
@@ -1335,7 +1425,8 @@ avro_schema_t avro_schema_copy_root(avro_schema_t schema, st_table *named_schema
 				    avro_schema_copy_root(val.field->type, named_schemas);
 				avro_schema_record_field_append(new_schema,
 								val.field->name,
-								type_copy);
+								type_copy,
+								val.field->field_id);
 				avro_schema_decref(type_copy);
 			}
 		}
@@ -1387,7 +1478,7 @@ avro_schema_t avro_schema_copy_root(avro_schema_t schema, st_table *named_schema
 			if (!values_copy) {
 				return NULL;
 			}
-			new_schema = avro_schema_map(values_copy);
+			new_schema = avro_schema_map(values_copy, map_schema->key_id, map_schema->value_id);
 			avro_schema_decref(values_copy);
 		}
 		break;
@@ -1401,7 +1492,7 @@ avro_schema_t avro_schema_copy_root(avro_schema_t schema, st_table *named_schema
 			if (!items_copy) {
 				return NULL;
 			}
-			new_schema = avro_schema_array(items_copy);
+			new_schema = avro_schema_array(items_copy, array_schema->element_id, array_schema->logical_type);
 			avro_schema_decref(items_copy);
 		}
 		break;
@@ -1683,6 +1774,16 @@ static int avro_write_str(avro_writer_t out, const char *str)
 {
 	return avro_write(out, (char *)str, strlen(str));
 }
+static int avro_write_int(avro_writer_t out, int32_t integer)
+{
+	char buffer[11];
+	
+	int ret = snprintf(buffer, 11, "%d", integer);
+	if (ret < 0) {
+		return 1;
+	}
+	return avro_write(out, buffer, ret);
+}
 
 static int write_field(avro_writer_t out, const struct avro_record_field_t *field,
 		       const char *parent_namespace)
@@ -1692,6 +1793,10 @@ static int write_field(avro_writer_t out, const struct avro_record_field_t *fiel
 	check(rval, avro_write_str(out, field->name));
 	check(rval, avro_write_str(out, "\",\"type\":"));
 	check(rval, avro_schema_to_json2(field->type, out, parent_namespace));
+	if (field->field_id != INT32_MAX) {
+		check(rval, avro_write_str(out, ",\"field-id\":"));
+		check(rval, avro_write_int(out, field->field_id));
+	}
 	return avro_write_str(out, "}");
 }
 
@@ -1784,7 +1889,16 @@ static int write_map(avro_writer_t out, const struct avro_map_schema_t *map,
 		     const char *parent_namespace)
 {
 	int rval;
-	check(rval, avro_write_str(out, "{\"type\":\"map\",\"values\":"));
+	check(rval, avro_write_str(out, "{\"type\":\"map\""));
+	if (map->key_id != INT32_MAX) {
+		check(rval, avro_write_str(out, ",\"key-id\":"));
+		check(rval, avro_write_int(out, map->key_id));
+	}
+	if (map->value_id != INT32_MAX) {
+		check(rval, avro_write_str(out, ",\"value-id\":"));
+		check(rval, avro_write_int(out, map->value_id));
+	}
+	check(rval, avro_write_str(out, ",\"values\":"));
 	check(rval, avro_schema_to_json2(map->values, out, parent_namespace));
 	return avro_write_str(out, "}");
 }
@@ -1792,7 +1906,17 @@ static int write_array(avro_writer_t out, const struct avro_array_schema_t *arra
 		       const char *parent_namespace)
 {
 	int rval;
-	check(rval, avro_write_str(out, "{\"type\":\"array\",\"items\":"));
+	check(rval, avro_write_str(out, "{\"type\":\"array\""));
+	if (array->element_id != INT32_MAX) {
+		check(rval, avro_write_str(out, ",\"element-id\":"));
+		check(rval, avro_write_int(out, array->element_id));
+	}
+	if (array->logical_type) {
+		check(rval, avro_write_str(out, ",\"logicalType\": \""));
+		check(rval, avro_write_str(out, array->logical_type));
+		check(rval, avro_write_str(out, "\""));
+	}
+	check(rval, avro_write_str(out, ",\"items\":"));
 	check(rval, avro_schema_to_json2(array->items, out, parent_namespace));
 	return avro_write_str(out, "}");
 }
diff --git a/lang/c/src/schema.h b/lang/c/src/schema.h
index 3c99ee630..a889fc595 100644
--- a/lang/c/src/schema.h
+++ b/lang/c/src/schema.h
@@ -27,6 +27,7 @@ struct avro_record_field_t {
 	int index;
 	char *name;
 	avro_schema_t type;
+	int32_t field_id;
 	/*
 	 * TODO: default values 
 	 */
@@ -51,11 +52,15 @@ struct avro_enum_schema_t {
 struct avro_array_schema_t {
 	struct avro_obj_t obj;
 	avro_schema_t items;
+	int32_t element_id;
+	char *logical_type;
 };
 
 struct avro_map_schema_t {
 	struct avro_obj_t obj;
 	avro_schema_t values;
+	int32_t key_id;
+	int32_t value_id;
 };
 
 struct avro_union_schema_t {
