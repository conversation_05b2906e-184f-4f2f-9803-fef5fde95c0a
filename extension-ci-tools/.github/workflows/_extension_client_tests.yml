# This is a reusable workflow to be used by extensions based on the extension template

name: Client Tests
on:
  workflow_call:
    inputs:
      duckdb_version:
        required: true
        type: string

jobs:
  python:
    name: Python
    runs-on: ubuntu-latest
    env:
      GEN: ninja

    steps:
      - name: Install Ninja
        run: |
          sudo apt-get update -y -qq
          sudo apt-get install -y -qq ninja-build

      - uses: actions/checkout@v2
        with:
          fetch-depth: 0
          submodules: 'recursive'

      - name: Checkout DuckDB to version
        run: |
          cd duckdb
          git checkout ${{ inputs.duckdb_version }}

      - uses: actions/setup-python@v5
        with:
          python-version: '3.9'

      - name: Build DuckDB Python client
        run: make debug_python

      - name: Install Python test dependencies
        run: python -m pip install --upgrade pytest

      - name: Run Python client tests
        run: |
          make test_debug_python