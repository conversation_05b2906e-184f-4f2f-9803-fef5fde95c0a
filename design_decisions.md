# DuckDB JSON Streaming Extension - Design Decisions

## Overview

This document captures the key design decisions made during the development of the DuckDB JSON streaming extension. These decisions were made to achieve the core objectives of memory efficiency, performance, and correctness.

## Core Architecture Decisions

### 1. Streaming vs. Materialization

**Decision**: Use streaming JSON parsing with direct vector population
**Rationale**: 
- Achieves O(row_size) memory usage instead of O(file_size)
- Enables processing of files larger than available memory
- Eliminates intermediate data structures and copying

**Implementation**: 
- Uses struson crate for streaming JSON parsing
- Populates DuckDB vectors directly during parsing
- Processes JSON in chunks with configurable limits

### 2. Schema Inference Strategy

**Decision**: Infer schema from first element with field order preservation
**Rationale**:
- Provides predictable column ordering for users
- Minimizes file scanning overhead
- Matches user expectations from other JSON tools

**Implementation**:
- Examines first object/array element to determine fields
- Preserves field order from first observation
- Stores schema in bind data for execution phase

### 3. Root Type Encoding

**Decision**: Encode JSON root type handling into schema structure
**Rationale**:
- Enables different processing strategies for different JSON shapes
- Provides clear separation of concerns
- Allows optimization for each root type

**Root Types Supported**:
- Single Object: `{"name": "<PERSON>", "age": 30}`
- Array of Objects: `[{"name": "Alice"}, {"name": "Bob"}]`
- Array of Primitives: `[1, 2, 3, 4]`
- Single Primitive: `42`

### 4. Vector Writing Architecture

**Decision**: Direct vector population with type-specific handling
**Rationale**:
- Eliminates intermediate data structures
- Provides optimal performance for DuckDB integration
- Enables proper type handling for different JSON values

**Implementation**:
- Separate functions for each JSON root type
- Direct writing to FlatVector for primitives
- JSON serialization for complex nested structures

## Memory Management Decisions

### 1. Chunk-Based Processing

**Decision**: Process arrays in chunks of 1000 rows
**Rationale**:
- Prevents memory exhaustion on large arrays
- Provides predictable memory usage patterns
- Allows for future configurability

### 2. String Handling

**Decision**: Use CString for DuckDB vector insertion
**Rationale**:
- Required by DuckDB's C API
- Ensures proper null termination
- Handles UTF-8 encoding correctly

### 3. Complex Type Serialization

**Decision**: Serialize nested objects/arrays as JSON strings
**Rationale**:
- Preserves all data without loss
- Provides readable output for users
- Enables future enhancement to native STRUCT/LIST vectors

## Error Handling Strategy

### 1. Fail-Fast Approach

**Decision**: Raise errors for unimplemented features instead of using placeholders
**Rationale**:
- Prevents silent data corruption
- Makes limitations explicit to users
- Encourages proper implementation

### 2. Graceful Degradation

**Decision**: Fallback to basic schema when inference fails
**Rationale**:
- Ensures extension remains functional
- Provides user feedback about issues
- Maintains system stability

## Performance Optimizations

### 1. Memory-Efficient Data Structures

**Decision**: Minimize size of core data structures
**Rationale**:
- Reduces memory overhead
- Improves cache performance
- Enables processing of larger datasets

**Measurements**:
- JsonValue: < 128 bytes
- JsonSchema: < 128 bytes  
- JsonRootType: ≤ 8 bytes

### 2. Streaming Parser Selection

**Decision**: Use struson crate for JSON parsing
**Rationale**:
- High-performance streaming parser
- Memory-efficient implementation
- Good Rust ecosystem integration

## Testing Strategy

### 1. Comprehensive Test Coverage

**Decision**: Test all JSON shapes and edge cases
**Rationale**:
- Ensures correctness across all supported formats
- Prevents regressions during development
- Validates memory efficiency claims

**Test Categories**:
- Unit tests for individual components
- Integration tests for end-to-end functionality
- Memory efficiency validation
- Edge case handling

### 2. Test-Driven Development

**Decision**: Write tests before implementing features
**Rationale**:
- Ensures features meet requirements
- Provides regression protection
- Documents expected behavior

## Future Enhancement Considerations

### 1. Native Vector Support

**Planned**: Implement native STRUCT and LIST vector support
**Benefits**:
- Better integration with DuckDB's type system
- Improved query performance on nested data
- Reduced memory usage for complex structures

### 2. Projection Pushdown

**Planned**: Only parse requested JSON paths
**Benefits**:
- Significant memory savings for wide objects
- Improved performance for selective queries
- Better scalability for large datasets

### 3. Configurable Chunk Sizes

**Planned**: Allow users to configure array processing chunk sizes
**Benefits**:
- Tunable memory vs. performance trade-offs
- Adaptation to different hardware configurations
- Optimization for specific use cases

## Lessons Learned

### 1. DuckDB Integration Complexity

**Learning**: DuckDB's vector API requires careful memory management
**Impact**: Influenced decision to use direct vector writing
**Future**: Consider wrapper abstractions for easier development

### 2. JSON Diversity

**Learning**: Real-world JSON has many edge cases and variations
**Impact**: Led to comprehensive test suite and robust error handling
**Future**: Continue expanding test coverage based on user feedback

### 3. Memory Efficiency Trade-offs

**Learning**: True streaming requires careful balance of features vs. memory usage
**Impact**: Influenced chunked processing and serialization decisions
**Future**: Monitor memory usage patterns in production use

## Conclusion

These design decisions prioritize memory efficiency, correctness, and performance while maintaining a clean, maintainable codebase. The architecture provides a solid foundation for future enhancements while delivering immediate value for JSON processing in DuckDB.
