[workspace]
resolver = "2"
members = [
    "crates/duckdb",
    "crates/libduckdb-sys",
    "crates/duckdb-loadable-macros",
]

[workspace.package]
version = "1.3.1"
authors = ["wangfenjin <<EMAIL>>"]
edition = "2021"
repository = "https://github.com/duckdb/duckdb-rs"
homepage = "https://github.com/duckdb/duckdb-rs"
documentation = "http://docs.rs/duckdb/"
readme = "README.md"
keywords = ["duckdb", "database", "ffi"]
license = "MIT"
categories = ["database"]

[workspace.dependencies]
duckdb = { version = "=1.3.1", path = "crates/duckdb" }
duckdb-loadable-macros = { version = "=0.1.8", path = "crates/duckdb-loadable-macros" }
libduckdb-sys = { version = "=1.3.1", path = "crates/libduckdb-sys" }

arrow = { version = "55", default-features = false }
bindgen = { version = "0.71.1", default-features = false }
calamine = "0.28.0"
cast = "0.3"
cc = "1.0"
chrono = { version = "0.4.22", default-features = false, features = [
    "std",
    "clock",
] }
doc-comment = "0.3"
fallible-iterator = "0.3"
fallible-streaming-iterator = "0.1"
flate2 = "1.0"
hashlink = "0.10"
num = { version = "0.4", default-features = false }
num-integer = "0.1.46"
pkg-config = "0.3.24"
polars = "0.49.1"
polars-arrow = "0.49.1"
polars-core = "0.49.1"
pretty_assertions = "1.4.0"
prettyplease = "0.2.20"
proc-macro2 = "1.0.56"
quote = "1.0.21"
r2d2 = "0.8.9"
rand = "0.9.0"
rust_decimal = "1.14"
serde = "1.0"
serde_json = "1.0"
smallvec = "1.6.1"
strum = "0.27"
syn = "2.0.15"
tar = "0.4.38"
tempfile = "3.1.0"
url = "2.1"
uuid = "1.0"
vcpkg = "0.2"
