name: CD

on:
  push:
    tags: ["*"]
env:
  RUST_BACKTRACE: 1
jobs:
  Release:
    name: Cargo Publish
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions-rust-lang/setup-rust-toolchain@v1

      # cargo publish
      - name: publish crates
        uses: katyo/publish-crates@v2
        with:
          path: "./"
          args: --no-verify --allow-dirty --all-features
          registry-token: ${{ secrets.CARGO_REGISTRY_TOKEN }}
          ignore-unpublished-changes: true

      # create release
      - name: "Build Changelog"
        id: build_changelog
        uses: mikepenz/release-changelog-builder-action@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      - name: Create Release
        uses: actions/create-release@v1
        with:
          tag_name: ${{ github.ref }}
          release_name: ${{ github.ref }}
          body: ${{steps.build_changelog.outputs.changelog}}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
