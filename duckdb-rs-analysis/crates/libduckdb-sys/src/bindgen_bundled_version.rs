/* automatically generated by rust-bindgen 0.71.1 */

pub const DUCKDB_TYPE_DUCKDB_TYPE_INVALID: DUCKDB_TYPE = 0;
pub const DUCKDB_TYPE_DUCKDB_TYPE_BOOLEAN: DUCKDB_TYPE = 1;
pub const DUCKDB_TYPE_DUCKDB_TYPE_TINYINT: DUCKDB_TYPE = 2;
pub const DUCKDB_TYPE_DUCKDB_TYPE_SMALLINT: DUCKDB_TYPE = 3;
pub const DUCKDB_TYPE_DUCKDB_TYPE_INTEGER: DUCKDB_TYPE = 4;
pub const DUCKDB_TYPE_DUCKDB_TYPE_BIGINT: DUCKDB_TYPE = 5;
pub const DUCKDB_TYPE_DUCKDB_TYPE_UTINYINT: DUCKDB_TYPE = 6;
pub const DUCKDB_TYPE_DUCKDB_TYPE_USMALLINT: DUCKDB_TYPE = 7;
pub const DUCKDB_TYPE_DUCKDB_TYPE_UINTEGER: DUCKDB_TYPE = 8;
pub const DUCKDB_TYPE_DUCKDB_TYPE_UBIGINT: DUCKDB_TYPE = 9;
pub const DUCKDB_TYPE_DUCKDB_TYPE_FLOAT: DUCKDB_TYPE = 10;
pub const DUCKDB_TYPE_DUCKDB_TYPE_DOUBLE: DUCKDB_TYPE = 11;
pub const DUCKDB_TYPE_DUCKDB_TYPE_TIMESTAMP: DUCKDB_TYPE = 12;
pub const DUCKDB_TYPE_DUCKDB_TYPE_DATE: DUCKDB_TYPE = 13;
pub const DUCKDB_TYPE_DUCKDB_TYPE_TIME: DUCKDB_TYPE = 14;
pub const DUCKDB_TYPE_DUCKDB_TYPE_INTERVAL: DUCKDB_TYPE = 15;
pub const DUCKDB_TYPE_DUCKDB_TYPE_HUGEINT: DUCKDB_TYPE = 16;
pub const DUCKDB_TYPE_DUCKDB_TYPE_UHUGEINT: DUCKDB_TYPE = 32;
pub const DUCKDB_TYPE_DUCKDB_TYPE_VARCHAR: DUCKDB_TYPE = 17;
pub const DUCKDB_TYPE_DUCKDB_TYPE_BLOB: DUCKDB_TYPE = 18;
pub const DUCKDB_TYPE_DUCKDB_TYPE_DECIMAL: DUCKDB_TYPE = 19;
pub const DUCKDB_TYPE_DUCKDB_TYPE_TIMESTAMP_S: DUCKDB_TYPE = 20;
pub const DUCKDB_TYPE_DUCKDB_TYPE_TIMESTAMP_MS: DUCKDB_TYPE = 21;
pub const DUCKDB_TYPE_DUCKDB_TYPE_TIMESTAMP_NS: DUCKDB_TYPE = 22;
pub const DUCKDB_TYPE_DUCKDB_TYPE_ENUM: DUCKDB_TYPE = 23;
pub const DUCKDB_TYPE_DUCKDB_TYPE_LIST: DUCKDB_TYPE = 24;
pub const DUCKDB_TYPE_DUCKDB_TYPE_STRUCT: DUCKDB_TYPE = 25;
pub const DUCKDB_TYPE_DUCKDB_TYPE_MAP: DUCKDB_TYPE = 26;
pub const DUCKDB_TYPE_DUCKDB_TYPE_ARRAY: DUCKDB_TYPE = 33;
pub const DUCKDB_TYPE_DUCKDB_TYPE_UUID: DUCKDB_TYPE = 27;
pub const DUCKDB_TYPE_DUCKDB_TYPE_UNION: DUCKDB_TYPE = 28;
pub const DUCKDB_TYPE_DUCKDB_TYPE_BIT: DUCKDB_TYPE = 29;
pub const DUCKDB_TYPE_DUCKDB_TYPE_TIME_TZ: DUCKDB_TYPE = 30;
pub const DUCKDB_TYPE_DUCKDB_TYPE_TIMESTAMP_TZ: DUCKDB_TYPE = 31;
pub const DUCKDB_TYPE_DUCKDB_TYPE_ANY: DUCKDB_TYPE = 34;
pub const DUCKDB_TYPE_DUCKDB_TYPE_VARINT: DUCKDB_TYPE = 35;
pub const DUCKDB_TYPE_DUCKDB_TYPE_SQLNULL: DUCKDB_TYPE = 36;
pub const DUCKDB_TYPE_DUCKDB_TYPE_STRING_LITERAL: DUCKDB_TYPE = 37;
pub const DUCKDB_TYPE_DUCKDB_TYPE_INTEGER_LITERAL: DUCKDB_TYPE = 38;
#[doc = "! An enum over DuckDB's internal types."]
pub type DUCKDB_TYPE = ::std::os::raw::c_uint;
#[doc = "! An enum over DuckDB's internal types."]
pub use self::DUCKDB_TYPE as duckdb_type;
pub const duckdb_state_DuckDBSuccess: duckdb_state = 0;
pub const duckdb_state_DuckDBError: duckdb_state = 1;
#[doc = "! An enum over the returned state of different functions."]
pub type duckdb_state = ::std::os::raw::c_uint;
pub const duckdb_pending_state_DUCKDB_PENDING_RESULT_READY: duckdb_pending_state = 0;
pub const duckdb_pending_state_DUCKDB_PENDING_RESULT_NOT_READY: duckdb_pending_state = 1;
pub const duckdb_pending_state_DUCKDB_PENDING_ERROR: duckdb_pending_state = 2;
pub const duckdb_pending_state_DUCKDB_PENDING_NO_TASKS_AVAILABLE: duckdb_pending_state = 3;
#[doc = "! An enum over the pending state of a pending query result."]
pub type duckdb_pending_state = ::std::os::raw::c_uint;
pub const duckdb_result_type_DUCKDB_RESULT_TYPE_INVALID: duckdb_result_type = 0;
pub const duckdb_result_type_DUCKDB_RESULT_TYPE_CHANGED_ROWS: duckdb_result_type = 1;
pub const duckdb_result_type_DUCKDB_RESULT_TYPE_NOTHING: duckdb_result_type = 2;
pub const duckdb_result_type_DUCKDB_RESULT_TYPE_QUERY_RESULT: duckdb_result_type = 3;
#[doc = "! An enum over DuckDB's different result types."]
pub type duckdb_result_type = ::std::os::raw::c_uint;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_INVALID: duckdb_statement_type = 0;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_SELECT: duckdb_statement_type = 1;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_INSERT: duckdb_statement_type = 2;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_UPDATE: duckdb_statement_type = 3;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_EXPLAIN: duckdb_statement_type = 4;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_DELETE: duckdb_statement_type = 5;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_PREPARE: duckdb_statement_type = 6;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_CREATE: duckdb_statement_type = 7;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_EXECUTE: duckdb_statement_type = 8;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_ALTER: duckdb_statement_type = 9;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_TRANSACTION: duckdb_statement_type = 10;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_COPY: duckdb_statement_type = 11;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_ANALYZE: duckdb_statement_type = 12;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_VARIABLE_SET: duckdb_statement_type = 13;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_CREATE_FUNC: duckdb_statement_type = 14;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_DROP: duckdb_statement_type = 15;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_EXPORT: duckdb_statement_type = 16;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_PRAGMA: duckdb_statement_type = 17;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_VACUUM: duckdb_statement_type = 18;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_CALL: duckdb_statement_type = 19;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_SET: duckdb_statement_type = 20;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_LOAD: duckdb_statement_type = 21;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_RELATION: duckdb_statement_type = 22;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_EXTENSION: duckdb_statement_type = 23;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_LOGICAL_PLAN: duckdb_statement_type = 24;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_ATTACH: duckdb_statement_type = 25;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_DETACH: duckdb_statement_type = 26;
pub const duckdb_statement_type_DUCKDB_STATEMENT_TYPE_MULTI: duckdb_statement_type = 27;
#[doc = "! An enum over DuckDB's different statement types."]
pub type duckdb_statement_type = ::std::os::raw::c_uint;
pub const duckdb_error_type_DUCKDB_ERROR_INVALID: duckdb_error_type = 0;
pub const duckdb_error_type_DUCKDB_ERROR_OUT_OF_RANGE: duckdb_error_type = 1;
pub const duckdb_error_type_DUCKDB_ERROR_CONVERSION: duckdb_error_type = 2;
pub const duckdb_error_type_DUCKDB_ERROR_UNKNOWN_TYPE: duckdb_error_type = 3;
pub const duckdb_error_type_DUCKDB_ERROR_DECIMAL: duckdb_error_type = 4;
pub const duckdb_error_type_DUCKDB_ERROR_MISMATCH_TYPE: duckdb_error_type = 5;
pub const duckdb_error_type_DUCKDB_ERROR_DIVIDE_BY_ZERO: duckdb_error_type = 6;
pub const duckdb_error_type_DUCKDB_ERROR_OBJECT_SIZE: duckdb_error_type = 7;
pub const duckdb_error_type_DUCKDB_ERROR_INVALID_TYPE: duckdb_error_type = 8;
pub const duckdb_error_type_DUCKDB_ERROR_SERIALIZATION: duckdb_error_type = 9;
pub const duckdb_error_type_DUCKDB_ERROR_TRANSACTION: duckdb_error_type = 10;
pub const duckdb_error_type_DUCKDB_ERROR_NOT_IMPLEMENTED: duckdb_error_type = 11;
pub const duckdb_error_type_DUCKDB_ERROR_EXPRESSION: duckdb_error_type = 12;
pub const duckdb_error_type_DUCKDB_ERROR_CATALOG: duckdb_error_type = 13;
pub const duckdb_error_type_DUCKDB_ERROR_PARSER: duckdb_error_type = 14;
pub const duckdb_error_type_DUCKDB_ERROR_PLANNER: duckdb_error_type = 15;
pub const duckdb_error_type_DUCKDB_ERROR_SCHEDULER: duckdb_error_type = 16;
pub const duckdb_error_type_DUCKDB_ERROR_EXECUTOR: duckdb_error_type = 17;
pub const duckdb_error_type_DUCKDB_ERROR_CONSTRAINT: duckdb_error_type = 18;
pub const duckdb_error_type_DUCKDB_ERROR_INDEX: duckdb_error_type = 19;
pub const duckdb_error_type_DUCKDB_ERROR_STAT: duckdb_error_type = 20;
pub const duckdb_error_type_DUCKDB_ERROR_CONNECTION: duckdb_error_type = 21;
pub const duckdb_error_type_DUCKDB_ERROR_SYNTAX: duckdb_error_type = 22;
pub const duckdb_error_type_DUCKDB_ERROR_SETTINGS: duckdb_error_type = 23;
pub const duckdb_error_type_DUCKDB_ERROR_BINDER: duckdb_error_type = 24;
pub const duckdb_error_type_DUCKDB_ERROR_NETWORK: duckdb_error_type = 25;
pub const duckdb_error_type_DUCKDB_ERROR_OPTIMIZER: duckdb_error_type = 26;
pub const duckdb_error_type_DUCKDB_ERROR_NULL_POINTER: duckdb_error_type = 27;
pub const duckdb_error_type_DUCKDB_ERROR_IO: duckdb_error_type = 28;
pub const duckdb_error_type_DUCKDB_ERROR_INTERRUPT: duckdb_error_type = 29;
pub const duckdb_error_type_DUCKDB_ERROR_FATAL: duckdb_error_type = 30;
pub const duckdb_error_type_DUCKDB_ERROR_INTERNAL: duckdb_error_type = 31;
pub const duckdb_error_type_DUCKDB_ERROR_INVALID_INPUT: duckdb_error_type = 32;
pub const duckdb_error_type_DUCKDB_ERROR_OUT_OF_MEMORY: duckdb_error_type = 33;
pub const duckdb_error_type_DUCKDB_ERROR_PERMISSION: duckdb_error_type = 34;
pub const duckdb_error_type_DUCKDB_ERROR_PARAMETER_NOT_RESOLVED: duckdb_error_type = 35;
pub const duckdb_error_type_DUCKDB_ERROR_PARAMETER_NOT_ALLOWED: duckdb_error_type = 36;
pub const duckdb_error_type_DUCKDB_ERROR_DEPENDENCY: duckdb_error_type = 37;
pub const duckdb_error_type_DUCKDB_ERROR_HTTP: duckdb_error_type = 38;
pub const duckdb_error_type_DUCKDB_ERROR_MISSING_EXTENSION: duckdb_error_type = 39;
pub const duckdb_error_type_DUCKDB_ERROR_AUTOLOAD: duckdb_error_type = 40;
pub const duckdb_error_type_DUCKDB_ERROR_SEQUENCE: duckdb_error_type = 41;
pub const duckdb_error_type_DUCKDB_INVALID_CONFIGURATION: duckdb_error_type = 42;
#[doc = "! An enum over DuckDB's different error types."]
pub type duckdb_error_type = ::std::os::raw::c_uint;
pub const duckdb_cast_mode_DUCKDB_CAST_NORMAL: duckdb_cast_mode = 0;
pub const duckdb_cast_mode_DUCKDB_CAST_TRY: duckdb_cast_mode = 1;
#[doc = "! An enum over DuckDB's different cast modes."]
pub type duckdb_cast_mode = ::std::os::raw::c_uint;
#[doc = "! DuckDB's index type."]
pub type idx_t = u64;
#[doc = "! Type used for the selection vector"]
pub type sel_t = u32;
#[doc = "! The callback that will be called to destroy data, e.g.,\n! bind data (if any), init data (if any), extra data for replacement scans (if any)"]
pub type duckdb_delete_callback_t = ::std::option::Option<unsafe extern "C" fn(data: *mut ::std::os::raw::c_void)>;
#[doc = "! Used for threading, contains a task state. Must be destroyed with `duckdb_destroy_task_state`."]
pub type duckdb_task_state = *mut ::std::os::raw::c_void;
#[doc = "! Days are stored as days since 1970-01-01\n! Use the duckdb_from_date/duckdb_to_date function to extract individual information"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_date {
    pub days: i32,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_date_struct {
    pub year: i32,
    pub month: i8,
    pub day: i8,
}
#[doc = "! Time is stored as microseconds since 00:00:00\n! Use the duckdb_from_time/duckdb_to_time function to extract individual information"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_time {
    pub micros: i64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_time_struct {
    pub hour: i8,
    pub min: i8,
    pub sec: i8,
    pub micros: i32,
}
#[doc = "! TIME_TZ is stored as 40 bits for int64_t micros, and 24 bits for int32_t offset"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_time_tz {
    pub bits: u64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_time_tz_struct {
    pub time: duckdb_time_struct,
    pub offset: i32,
}
#[doc = "! TIMESTAMP values are stored as microseconds since 1970-01-01.\n! Use the duckdb_from_timestamp and duckdb_to_timestamp functions to extract individual information."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_timestamp {
    pub micros: i64,
}
#[doc = "! TIMESTAMP_S values are stored as seconds since 1970-01-01."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_timestamp_s {
    pub seconds: i64,
}
#[doc = "! TIMESTAMP_MS values are stored as milliseconds since 1970-01-01."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_timestamp_ms {
    pub millis: i64,
}
#[doc = "! TIMESTAMP_NS values are stored as nanoseconds since 1970-01-01."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_timestamp_ns {
    pub nanos: i64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_timestamp_struct {
    pub date: duckdb_date_struct,
    pub time: duckdb_time_struct,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_interval {
    pub months: i32,
    pub days: i32,
    pub micros: i64,
}
#[doc = "! Hugeints are composed of a (lower, upper) component\n! The value of the hugeint is upper * 2^64 + lower\n! For easy usage, the functions duckdb_hugeint_to_double/duckdb_double_to_hugeint are recommended"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_hugeint {
    pub lower: u64,
    pub upper: i64,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_uhugeint {
    pub lower: u64,
    pub upper: u64,
}
#[doc = "! Decimals are composed of a width and a scale, and are stored in a hugeint"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_decimal {
    pub width: u8,
    pub scale: u8,
    pub value: duckdb_hugeint,
}
#[doc = "! A type holding information about the query execution progress"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_query_progress_type {
    pub percentage: f64,
    pub rows_processed: u64,
    pub total_rows_to_process: u64,
}
#[doc = "! The internal representation of a VARCHAR (string_t). If the VARCHAR does not\n! exceed 12 characters, then we inline it. Otherwise, we inline a prefix for faster\n! string comparisons and store a pointer to the remaining characters. This is a non-\n! owning structure, i.e., it does not have to be freed."]
#[repr(C)]
#[derive(Copy, Clone)]
pub struct duckdb_string_t {
    pub value: duckdb_string_t__bindgen_ty_1,
}
#[repr(C)]
#[derive(Copy, Clone)]
pub union duckdb_string_t__bindgen_ty_1 {
    pub pointer: duckdb_string_t__bindgen_ty_1__bindgen_ty_1,
    pub inlined: duckdb_string_t__bindgen_ty_1__bindgen_ty_2,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_string_t__bindgen_ty_1__bindgen_ty_1 {
    pub length: u32,
    pub prefix: [::std::os::raw::c_char; 4usize],
    pub ptr: *mut ::std::os::raw::c_char,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_string_t__bindgen_ty_1__bindgen_ty_2 {
    pub length: u32,
    pub inlined: [::std::os::raw::c_char; 12usize],
}
#[doc = "! The internal representation of a list metadata entry contains the list's offset in\n! the child vector, and its length. The parent vector holds these metadata entries,\n! whereas the child vector holds the data"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_list_entry {
    pub offset: u64,
    pub length: u64,
}
#[doc = "! A column consists of a pointer to its internal data. Don't operate on this type directly.\n! Instead, use functions such as duckdb_column_data, duckdb_nullmask_data,\n! duckdb_column_type, and duckdb_column_name, which take the result and the column index\n! as their parameters"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_column {
    pub deprecated_data: *mut ::std::os::raw::c_void,
    pub deprecated_nullmask: *mut bool,
    pub deprecated_type: duckdb_type,
    pub deprecated_name: *mut ::std::os::raw::c_char,
    pub internal_data: *mut ::std::os::raw::c_void,
}
#[doc = "! A vector to a specified column in a data chunk. Lives as long as the\n! data chunk lives, i.e., must not be destroyed."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_vector {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A vector to a specified column in a data chunk. Lives as long as the\n! data chunk lives, i.e., must not be destroyed."]
pub type duckdb_vector = *mut _duckdb_vector;
#[doc = "! A selection vector is a possibly duplicative vector of indices, which refer to values in a vector.\n! The resulting vector is make up of the values at each index in the selection vector."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_selection_vector {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A selection vector is a possibly duplicative vector of indices, which refer to values in a vector.\n! The resulting vector is make up of the values at each index in the selection vector."]
pub type duckdb_selection_vector = *mut _duckdb_selection_vector;
#[doc = "! Strings are composed of a char pointer and a size. You must free string.data\n! with `duckdb_free`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_string {
    pub data: *mut ::std::os::raw::c_char,
    pub size: idx_t,
}
#[doc = "! BLOBs are composed of a byte pointer and a size. You must free blob.data\n! with `duckdb_free`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_blob {
    pub data: *mut ::std::os::raw::c_void,
    pub size: idx_t,
}
#[doc = "! BITs are composed of a byte pointer and a size.\n! BIT byte data has 0 to 7 bits of padding.\n! The first byte contains the number of padding bits.\n! This number of bits of the second byte are set to 1, starting from the MSB.\n! You must free `data` with `duckdb_free`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_bit {
    pub data: *mut u8,
    pub size: idx_t,
}
#[doc = "! VARINTs are composed of a byte pointer, a size, and an is_negative bool.\n! The absolute value of the number is stored in `data` in little endian format.\n! You must free `data` with `duckdb_free`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_varint {
    pub data: *mut u8,
    pub size: idx_t,
    pub is_negative: bool,
}
#[doc = "! A query result consists of a pointer to its internal data.\n! Must be freed with 'duckdb_destroy_result'."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_result {
    pub deprecated_column_count: idx_t,
    pub deprecated_row_count: idx_t,
    pub deprecated_rows_changed: idx_t,
    pub deprecated_columns: *mut duckdb_column,
    pub deprecated_error_message: *mut ::std::os::raw::c_char,
    pub internal_data: *mut ::std::os::raw::c_void,
}
#[doc = "! A database instance cache object. Must be destroyed with `duckdb_destroy_instance_cache`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_instance_cache {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A database instance cache object. Must be destroyed with `duckdb_destroy_instance_cache`."]
pub type duckdb_instance_cache = *mut _duckdb_instance_cache;
#[doc = "! A database object. Must be closed with `duckdb_close`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_database {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A database object. Must be closed with `duckdb_close`."]
pub type duckdb_database = *mut _duckdb_database;
#[doc = "! A connection to a duckdb database. Must be closed with `duckdb_disconnect`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_connection {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A connection to a duckdb database. Must be closed with `duckdb_disconnect`."]
pub type duckdb_connection = *mut _duckdb_connection;
#[doc = "! A client context of a duckdb connection. Must be destroyed with `duckdb_destroy_context`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_client_context {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A client context of a duckdb connection. Must be destroyed with `duckdb_destroy_context`."]
pub type duckdb_client_context = *mut _duckdb_client_context;
#[doc = "! A prepared statement is a parameterized query that allows you to bind parameters to it.\n! Must be destroyed with `duckdb_destroy_prepare`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_prepared_statement {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A prepared statement is a parameterized query that allows you to bind parameters to it.\n! Must be destroyed with `duckdb_destroy_prepare`."]
pub type duckdb_prepared_statement = *mut _duckdb_prepared_statement;
#[doc = "! Extracted statements. Must be destroyed with `duckdb_destroy_extracted`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_extracted_statements {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Extracted statements. Must be destroyed with `duckdb_destroy_extracted`."]
pub type duckdb_extracted_statements = *mut _duckdb_extracted_statements;
#[doc = "! The pending result represents an intermediate structure for a query that is not yet fully executed.\n! Must be destroyed with `duckdb_destroy_pending`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_pending_result {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! The pending result represents an intermediate structure for a query that is not yet fully executed.\n! Must be destroyed with `duckdb_destroy_pending`."]
pub type duckdb_pending_result = *mut _duckdb_pending_result;
#[doc = "! The appender enables fast data loading into DuckDB.\n! Must be destroyed with `duckdb_appender_destroy`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_appender {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! The appender enables fast data loading into DuckDB.\n! Must be destroyed with `duckdb_appender_destroy`."]
pub type duckdb_appender = *mut _duckdb_appender;
#[doc = "! The table description allows querying info about the table.\n! Must be destroyed with `duckdb_table_description_destroy`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_table_description {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! The table description allows querying info about the table.\n! Must be destroyed with `duckdb_table_description_destroy`."]
pub type duckdb_table_description = *mut _duckdb_table_description;
#[doc = "! Can be used to provide start-up options for the DuckDB instance.\n! Must be destroyed with `duckdb_destroy_config`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_config {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Can be used to provide start-up options for the DuckDB instance.\n! Must be destroyed with `duckdb_destroy_config`."]
pub type duckdb_config = *mut _duckdb_config;
#[doc = "! Holds an internal logical type.\n! Must be destroyed with `duckdb_destroy_logical_type`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_logical_type {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds an internal logical type.\n! Must be destroyed with `duckdb_destroy_logical_type`."]
pub type duckdb_logical_type = *mut _duckdb_logical_type;
#[doc = "! Holds extra information used when registering a custom logical type.\n! Reserved for future use."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_create_type_info {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds extra information used when registering a custom logical type.\n! Reserved for future use."]
pub type duckdb_create_type_info = *mut _duckdb_create_type_info;
#[doc = "! Contains a data chunk from a duckdb_result.\n! Must be destroyed with `duckdb_destroy_data_chunk`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_data_chunk {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Contains a data chunk from a duckdb_result.\n! Must be destroyed with `duckdb_destroy_data_chunk`."]
pub type duckdb_data_chunk = *mut _duckdb_data_chunk;
#[doc = "! Holds a DuckDB value, which wraps a type.\n! Must be destroyed with `duckdb_destroy_value`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_value {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds a DuckDB value, which wraps a type.\n! Must be destroyed with `duckdb_destroy_value`."]
pub type duckdb_value = *mut _duckdb_value;
#[doc = "! Holds a recursive tree that matches the query plan."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_profiling_info {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds a recursive tree that matches the query plan."]
pub type duckdb_profiling_info = *mut _duckdb_profiling_info;
#[doc = "! Holds state during the C API extension intialization process"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_extension_info {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds state during the C API extension intialization process"]
pub type duckdb_extension_info = *mut _duckdb_extension_info;
#[doc = "! Additional function info.\n! When setting this info, it is necessary to pass a destroy-callback function."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_function_info {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Additional function info.\n! When setting this info, it is necessary to pass a destroy-callback function."]
pub type duckdb_function_info = *mut _duckdb_function_info;
#[doc = "! The bind info of a function.\n! When setting this info, it is necessary to pass a destroy-callback function."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_bind_info {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! The bind info of a function.\n! When setting this info, it is necessary to pass a destroy-callback function."]
pub type duckdb_bind_info = *mut _duckdb_bind_info;
#[doc = "! A scalar function. Must be destroyed with `duckdb_destroy_scalar_function`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_scalar_function {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A scalar function. Must be destroyed with `duckdb_destroy_scalar_function`."]
pub type duckdb_scalar_function = *mut _duckdb_scalar_function;
#[doc = "! A scalar function set. Must be destroyed with `duckdb_destroy_scalar_function_set`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_scalar_function_set {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A scalar function set. Must be destroyed with `duckdb_destroy_scalar_function_set`."]
pub type duckdb_scalar_function_set = *mut _duckdb_scalar_function_set;
#[doc = "! The bind function of the scalar function."]
pub type duckdb_scalar_function_bind_t = ::std::option::Option<unsafe extern "C" fn(info: duckdb_bind_info)>;
#[doc = "! The main function of the scalar function."]
pub type duckdb_scalar_function_t = ::std::option::Option<
    unsafe extern "C" fn(info: duckdb_function_info, input: duckdb_data_chunk, output: duckdb_vector),
>;
#[doc = "! An aggregate function. Must be destroyed with `duckdb_destroy_aggregate_function`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_aggregate_function {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! An aggregate function. Must be destroyed with `duckdb_destroy_aggregate_function`."]
pub type duckdb_aggregate_function = *mut _duckdb_aggregate_function;
#[doc = "! A aggregate function set. Must be destroyed with `duckdb_destroy_aggregate_function_set`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_aggregate_function_set {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A aggregate function set. Must be destroyed with `duckdb_destroy_aggregate_function_set`."]
pub type duckdb_aggregate_function_set = *mut _duckdb_aggregate_function_set;
#[doc = "! Aggregate state"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_aggregate_state {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Aggregate state"]
pub type duckdb_aggregate_state = *mut _duckdb_aggregate_state;
#[doc = "! Returns the aggregate state size"]
pub type duckdb_aggregate_state_size = ::std::option::Option<unsafe extern "C" fn(info: duckdb_function_info) -> idx_t>;
#[doc = "! Initialize the aggregate state"]
pub type duckdb_aggregate_init_t =
    ::std::option::Option<unsafe extern "C" fn(info: duckdb_function_info, state: duckdb_aggregate_state)>;
#[doc = "! Destroy aggregate state (optional)"]
pub type duckdb_aggregate_destroy_t =
    ::std::option::Option<unsafe extern "C" fn(states: *mut duckdb_aggregate_state, count: idx_t)>;
#[doc = "! Update a set of aggregate states with new values"]
pub type duckdb_aggregate_update_t = ::std::option::Option<
    unsafe extern "C" fn(info: duckdb_function_info, input: duckdb_data_chunk, states: *mut duckdb_aggregate_state),
>;
#[doc = "! Combine aggregate states"]
pub type duckdb_aggregate_combine_t = ::std::option::Option<
    unsafe extern "C" fn(
        info: duckdb_function_info,
        source: *mut duckdb_aggregate_state,
        target: *mut duckdb_aggregate_state,
        count: idx_t,
    ),
>;
#[doc = "! Finalize aggregate states into a result vector"]
pub type duckdb_aggregate_finalize_t = ::std::option::Option<
    unsafe extern "C" fn(
        info: duckdb_function_info,
        source: *mut duckdb_aggregate_state,
        result: duckdb_vector,
        count: idx_t,
        offset: idx_t,
    ),
>;
#[doc = "! A table function. Must be destroyed with `duckdb_destroy_table_function`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_table_function {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A table function. Must be destroyed with `duckdb_destroy_table_function`."]
pub type duckdb_table_function = *mut _duckdb_table_function;
#[doc = "! Additional function init info. When setting this info, it is necessary to pass a destroy-callback function."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_init_info {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Additional function init info. When setting this info, it is necessary to pass a destroy-callback function."]
pub type duckdb_init_info = *mut _duckdb_init_info;
#[doc = "! The bind function of the table function."]
pub type duckdb_table_function_bind_t = ::std::option::Option<unsafe extern "C" fn(info: duckdb_bind_info)>;
#[doc = "! The (possibly thread-local) init function of the table function."]
pub type duckdb_table_function_init_t = ::std::option::Option<unsafe extern "C" fn(info: duckdb_init_info)>;
#[doc = "! The main function of the table function."]
pub type duckdb_table_function_t =
    ::std::option::Option<unsafe extern "C" fn(info: duckdb_function_info, output: duckdb_data_chunk)>;
#[doc = "! A cast function. Must be destroyed with `duckdb_destroy_cast_function`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_cast_function {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! A cast function. Must be destroyed with `duckdb_destroy_cast_function`."]
pub type duckdb_cast_function = *mut _duckdb_cast_function;
pub type duckdb_cast_function_t = ::std::option::Option<
    unsafe extern "C" fn(info: duckdb_function_info, count: idx_t, input: duckdb_vector, output: duckdb_vector) -> bool,
>;
#[doc = "! Additional replacement scan info. When setting this info, it is necessary to pass a destroy-callback function."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_replacement_scan_info {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Additional replacement scan info. When setting this info, it is necessary to pass a destroy-callback function."]
pub type duckdb_replacement_scan_info = *mut _duckdb_replacement_scan_info;
#[doc = "! A replacement scan function that can be added to a database."]
pub type duckdb_replacement_callback_t = ::std::option::Option<
    unsafe extern "C" fn(
        info: duckdb_replacement_scan_info,
        table_name: *const ::std::os::raw::c_char,
        data: *mut ::std::os::raw::c_void,
    ),
>;
#[doc = "! Holds an arrow query result. Must be destroyed with `duckdb_destroy_arrow`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_arrow {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds an arrow query result. Must be destroyed with `duckdb_destroy_arrow`."]
pub type duckdb_arrow = *mut _duckdb_arrow;
#[doc = "! Holds an arrow array stream. Must be destroyed with `duckdb_destroy_arrow_stream`."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_arrow_stream {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds an arrow array stream. Must be destroyed with `duckdb_destroy_arrow_stream`."]
pub type duckdb_arrow_stream = *mut _duckdb_arrow_stream;
#[doc = "! Holds an arrow schema. Remember to release the respective ArrowSchema object."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_arrow_schema {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds an arrow schema. Remember to release the respective ArrowSchema object."]
pub type duckdb_arrow_schema = *mut _duckdb_arrow_schema;
#[doc = "! Holds an arrow array. Remember to release the respective ArrowArray object."]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct _duckdb_arrow_array {
    pub internal_ptr: *mut ::std::os::raw::c_void,
}
#[doc = "! Holds an arrow array. Remember to release the respective ArrowArray object."]
pub type duckdb_arrow_array = *mut _duckdb_arrow_array;
#[doc = "! Passed to C API extension as parameter to the entrypoint"]
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct duckdb_extension_access {
    #[doc = "! Indicate that an error has occurred"]
    pub set_error:
        ::std::option::Option<unsafe extern "C" fn(info: duckdb_extension_info, error: *const ::std::os::raw::c_char)>,
    #[doc = "! Fetch the database from duckdb to register extensions to"]
    pub get_database: ::std::option::Option<unsafe extern "C" fn(info: duckdb_extension_info) -> *mut duckdb_database>,
    #[doc = "! Fetch the API"]
    pub get_api: ::std::option::Option<
        unsafe extern "C" fn(
            info: duckdb_extension_info,
            version: *const ::std::os::raw::c_char,
        ) -> *const ::std::os::raw::c_void,
    >,
}
unsafe extern "C" {
    #[doc = "Creates a new database instance cache.\nThe instance cache is necessary if a client/program (re)opens multiple databases to the same file within the same\nprocess. Must be destroyed with 'duckdb_destroy_instance_cache'.\n\n @return The database instance cache."]
    pub fn duckdb_create_instance_cache() -> duckdb_instance_cache;
}
unsafe extern "C" {
    #[doc = "Creates a new database instance in the instance cache, or retrieves an existing database instance.\nMust be closed with 'duckdb_close'.\n\n @param instance_cache The instance cache in which to create the database, or from which to take the database.\n @param path Path to the database file on disk. Both `nullptr` and `:memory:` open or retrieve an in-memory database.\n @param out_database The resulting cached database.\n @param config (Optional) configuration used to create the database.\n @param out_error If set and the function returns `DuckDBError`, this contains the error message.\nNote that the error message must be freed using `duckdb_free`.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_get_or_create_from_cache(
        instance_cache: duckdb_instance_cache,
        path: *const ::std::os::raw::c_char,
        out_database: *mut duckdb_database,
        config: duckdb_config,
        out_error: *mut *mut ::std::os::raw::c_char,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Destroys an existing database instance cache and de-allocates its memory.\n\n @param instance_cache The instance cache to destroy."]
    pub fn duckdb_destroy_instance_cache(instance_cache: *mut duckdb_instance_cache);
}
unsafe extern "C" {
    #[doc = "Creates a new database or opens an existing database file stored at the given path.\nIf no path is given a new in-memory database is created instead.\nThe database must be closed with 'duckdb_close'.\n\n @param path Path to the database file on disk. Both `nullptr` and `:memory:` open an in-memory database.\n @param out_database The result database object.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_open(path: *const ::std::os::raw::c_char, out_database: *mut duckdb_database) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Extended version of duckdb_open. Creates a new database or opens an existing database file stored at the given path.\nThe database must be closed with 'duckdb_close'.\n\n @param path Path to the database file on disk. Both `nullptr` and `:memory:` open an in-memory database.\n @param out_database The result database object.\n @param config (Optional) configuration used to start up the database.\n @param out_error If set and the function returns `DuckDBError`, this contains the error message.\nNote that the error message must be freed using `duckdb_free`.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_open_ext(
        path: *const ::std::os::raw::c_char,
        out_database: *mut duckdb_database,
        config: duckdb_config,
        out_error: *mut *mut ::std::os::raw::c_char,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Closes the specified database and de-allocates all memory allocated for that database.\nThis should be called after you are done with any database allocated through `duckdb_open` or `duckdb_open_ext`.\nNote that failing to call `duckdb_close` (in case of e.g. a program crash) will not cause data corruption.\nStill, it is recommended to always correctly close a database object after you are done with it.\n\n @param database The database object to shut down."]
    pub fn duckdb_close(database: *mut duckdb_database);
}
unsafe extern "C" {
    #[doc = "Opens a connection to a database. Connections are required to query the database, and store transactional state\nassociated with the connection.\nThe instantiated connection should be closed using 'duckdb_disconnect'.\n\n @param database The database file to connect to.\n @param out_connection The result connection object.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_connect(database: duckdb_database, out_connection: *mut duckdb_connection) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Interrupt running query\n\n @param connection The connection to interrupt"]
    pub fn duckdb_interrupt(connection: duckdb_connection);
}
unsafe extern "C" {
    #[doc = "Get progress of the running query\n\n @param connection The working connection\n @return -1 if no progress or a percentage of the progress"]
    pub fn duckdb_query_progress(connection: duckdb_connection) -> duckdb_query_progress_type;
}
unsafe extern "C" {
    #[doc = "Closes the specified connection and de-allocates all memory allocated for that connection.\n\n @param connection The connection to close."]
    pub fn duckdb_disconnect(connection: *mut duckdb_connection);
}
unsafe extern "C" {
    #[doc = "Retrieves the client context of the connection.\n\n @param connection The connection.\n @param out_context The client context of the connection. Must be destroyed with `duckdb_destroy_client_context`."]
    pub fn duckdb_connection_get_client_context(connection: duckdb_connection, out_context: *mut duckdb_client_context);
}
unsafe extern "C" {
    #[doc = "Returns the connection id of the client context.\n\n @param context The client context.\n @return The connection id of the client context."]
    pub fn duckdb_client_context_get_connection_id(context: duckdb_client_context) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Destroys the client context and deallocates its memory.\n\n @param context The client context to destroy."]
    pub fn duckdb_destroy_client_context(context: *mut duckdb_client_context);
}
unsafe extern "C" {
    #[doc = "Returns the version of the linked DuckDB, with a version postfix for dev versions\n\nUsually used for developing C extensions that must return this for a compatibility check."]
    pub fn duckdb_library_version() -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Get the list of (fully qualified) table names of the query.\n\n @param connection The connection for which to get the table names.\n @param query The query for which to get the table names.\n @param qualified Returns fully qualified table names (catalog.schema.table), if set to true, else only the (not\nescaped) table names.\n @return A duckdb_value of type VARCHAR[] containing the (fully qualified) table names of the query. Must be destroyed\nwith duckdb_destroy_value."]
    pub fn duckdb_get_table_names(
        connection: duckdb_connection,
        query: *const ::std::os::raw::c_char,
        qualified: bool,
    ) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Initializes an empty configuration object that can be used to provide start-up options for the DuckDB instance\nthrough `duckdb_open_ext`.\nThe duckdb_config must be destroyed using 'duckdb_destroy_config'\n\nThis will always succeed unless there is a malloc failure.\n\nNote that `duckdb_destroy_config` should always be called on the resulting config, even if the function returns\n`DuckDBError`.\n\n @param out_config The result configuration object.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_create_config(out_config: *mut duckdb_config) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "This returns the total amount of configuration options available for usage with `duckdb_get_config_flag`.\n\nThis should not be called in a loop as it internally loops over all the options.\n\n @return The amount of config options available."]
    pub fn duckdb_config_count() -> usize;
}
unsafe extern "C" {
    #[doc = "Obtains a human-readable name and description of a specific configuration option. This can be used to e.g.\ndisplay configuration options. This will succeed unless `index` is out of range (i.e. `>= duckdb_config_count`).\n\nThe result name or description MUST NOT be freed.\n\n @param index The index of the configuration option (between 0 and `duckdb_config_count`)\n @param out_name A name of the configuration flag.\n @param out_description A description of the configuration flag.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_get_config_flag(
        index: usize,
        out_name: *mut *const ::std::os::raw::c_char,
        out_description: *mut *const ::std::os::raw::c_char,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Sets the specified option for the specified configuration. The configuration option is indicated by name.\nTo obtain a list of config options, see `duckdb_get_config_flag`.\n\nIn the source code, configuration options are defined in `config.cpp`.\n\nThis can fail if either the name is invalid, or if the value provided for the option is invalid.\n\n @param config The configuration object to set the option on.\n @param name The name of the configuration flag to set.\n @param option The value to set the configuration flag to.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_set_config(
        config: duckdb_config,
        name: *const ::std::os::raw::c_char,
        option: *const ::std::os::raw::c_char,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Destroys the specified configuration object and de-allocates all memory allocated for the object.\n\n @param config The configuration object to destroy."]
    pub fn duckdb_destroy_config(config: *mut duckdb_config);
}
unsafe extern "C" {
    #[doc = "Executes a SQL query within a connection and stores the full (materialized) result in the out_result pointer.\nIf the query fails to execute, DuckDBError is returned and the error message can be retrieved by calling\n`duckdb_result_error`.\n\nNote that after running `duckdb_query`, `duckdb_destroy_result` must be called on the result object even if the\nquery fails, otherwise the error stored within the result will not be freed correctly.\n\n @param connection The connection to perform the query in.\n @param query The SQL query to run.\n @param out_result The query result.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_query(
        connection: duckdb_connection,
        query: *const ::std::os::raw::c_char,
        out_result: *mut duckdb_result,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Closes the result and de-allocates all memory allocated for that result.\n\n @param result The result to destroy."]
    pub fn duckdb_destroy_result(result: *mut duckdb_result);
}
unsafe extern "C" {
    #[doc = "Returns the column name of the specified column. The result should not need to be freed; the column names will\nautomatically be destroyed when the result is destroyed.\n\nReturns `NULL` if the column is out of range.\n\n @param result The result object to fetch the column name from.\n @param col The column index.\n @return The column name of the specified column."]
    pub fn duckdb_column_name(result: *mut duckdb_result, col: idx_t) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Returns the column type of the specified column.\n\nReturns `DUCKDB_TYPE_INVALID` if the column is out of range.\n\n @param result The result object to fetch the column type from.\n @param col The column index.\n @return The column type of the specified column."]
    pub fn duckdb_column_type(result: *mut duckdb_result, col: idx_t) -> duckdb_type;
}
unsafe extern "C" {
    #[doc = "Returns the statement type of the statement that was executed\n\n @param result The result object to fetch the statement type from.\n @return duckdb_statement_type value or DUCKDB_STATEMENT_TYPE_INVALID"]
    pub fn duckdb_result_statement_type(result: duckdb_result) -> duckdb_statement_type;
}
unsafe extern "C" {
    #[doc = "Returns the logical column type of the specified column.\n\nThe return type of this call should be destroyed with `duckdb_destroy_logical_type`.\n\nReturns `NULL` if the column is out of range.\n\n @param result The result object to fetch the column type from.\n @param col The column index.\n @return The logical column type of the specified column."]
    pub fn duckdb_column_logical_type(result: *mut duckdb_result, col: idx_t) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Returns the number of columns present in a the result object.\n\n @param result The result object.\n @return The number of columns present in the result object."]
    pub fn duckdb_column_count(result: *mut duckdb_result) -> idx_t;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nReturns the number of rows present in the result object.\n\n @param result The result object.\n @return The number of rows present in the result object."]
    pub fn duckdb_row_count(result: *mut duckdb_result) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Returns the number of rows changed by the query stored in the result. This is relevant only for INSERT/UPDATE/DELETE\nqueries. For other queries the rows_changed will be 0.\n\n @param result The result object.\n @return The number of rows changed."]
    pub fn duckdb_rows_changed(result: *mut duckdb_result) -> idx_t;
}
unsafe extern "C" {
    #[doc = "DEPRECATED**: Prefer using `duckdb_result_get_chunk` instead.\n\nReturns the data of a specific column of a result in columnar format.\n\nThe function returns a dense array which contains the result data. The exact type stored in the array depends on the\ncorresponding duckdb_type (as provided by `duckdb_column_type`). For the exact type by which the data should be\naccessed, see the comments in [the types section](types) or the `DUCKDB_TYPE` enum.\n\nFor example, for a column of type `DUCKDB_TYPE_INTEGER`, rows can be accessed in the following manner:\n```c\nint32_t *data = (int32_t *) duckdb_column_data(&result, 0);\nprintf(\"Data for row %d: %d\\n\", row, data[row]);\n```\n\n @param result The result object to fetch the column data from.\n @param col The column index.\n @return The column data of the specified column."]
    pub fn duckdb_column_data(result: *mut duckdb_result, col: idx_t) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "DEPRECATED**: Prefer using `duckdb_result_get_chunk` instead.\n\nReturns the nullmask of a specific column of a result in columnar format. The nullmask indicates for every row\nwhether or not the corresponding row is `NULL`. If a row is `NULL`, the values present in the array provided\nby `duckdb_column_data` are undefined.\n\n```c\nint32_t *data = (int32_t *) duckdb_column_data(&result, 0);\nbool *nullmask = duckdb_nullmask_data(&result, 0);\nif (nullmask[row]) {\nprintf(\"Data for row %d: NULL\\n\", row);\n} else {\nprintf(\"Data for row %d: %d\\n\", row, data[row]);\n}\n```\n\n @param result The result object to fetch the nullmask from.\n @param col The column index.\n @return The nullmask of the specified column."]
    pub fn duckdb_nullmask_data(result: *mut duckdb_result, col: idx_t) -> *mut bool;
}
unsafe extern "C" {
    #[doc = "Returns the error message contained within the result. The error is only set if `duckdb_query` returns `DuckDBError`.\n\nThe result of this function must not be freed. It will be cleaned up when `duckdb_destroy_result` is called.\n\n @param result The result object to fetch the error from.\n @return The error of the result."]
    pub fn duckdb_result_error(result: *mut duckdb_result) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Returns the result error type contained within the result. The error is only set if `duckdb_query` returns\n`DuckDBError`.\n\n @param result The result object to fetch the error from.\n @return The error type of the result."]
    pub fn duckdb_result_error_type(result: *mut duckdb_result) -> duckdb_error_type;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nFetches a data chunk from the duckdb_result. This function should be called repeatedly until the result is exhausted.\n\nThe result must be destroyed with `duckdb_destroy_data_chunk`.\n\nThis function supersedes all `duckdb_value` functions, as well as the `duckdb_column_data` and `duckdb_nullmask_data`\nfunctions. It results in significantly better performance, and should be preferred in newer code-bases.\n\nIf this function is used, none of the other result functions can be used and vice versa (i.e. this function cannot be\nmixed with the legacy result functions).\n\nUse `duckdb_result_chunk_count` to figure out how many chunks there are in the result.\n\n @param result The result object to fetch the data chunk from.\n @param chunk_index The chunk index to fetch from.\n @return The resulting data chunk. Returns `NULL` if the chunk index is out of bounds."]
    pub fn duckdb_result_get_chunk(result: duckdb_result, chunk_index: idx_t) -> duckdb_data_chunk;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nChecks if the type of the internal result is StreamQueryResult.\n\n @param result The result object to check.\n @return Whether or not the result object is of the type StreamQueryResult"]
    pub fn duckdb_result_is_streaming(result: duckdb_result) -> bool;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nReturns the number of data chunks present in the result.\n\n @param result The result object\n @return Number of data chunks present in the result."]
    pub fn duckdb_result_chunk_count(result: duckdb_result) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Returns the return_type of the given result, or DUCKDB_RETURN_TYPE_INVALID on error\n\n @param result The result object\n @return The return_type"]
    pub fn duckdb_result_return_type(result: duckdb_result) -> duckdb_result_type;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The boolean value at the specified location, or false if the value cannot be converted."]
    pub fn duckdb_value_boolean(result: *mut duckdb_result, col: idx_t, row: idx_t) -> bool;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The int8_t value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_int8(result: *mut duckdb_result, col: idx_t, row: idx_t) -> i8;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The int16_t value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_int16(result: *mut duckdb_result, col: idx_t, row: idx_t) -> i16;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The int32_t value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_int32(result: *mut duckdb_result, col: idx_t, row: idx_t) -> i32;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The int64_t value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_int64(result: *mut duckdb_result, col: idx_t, row: idx_t) -> i64;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The duckdb_hugeint value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_hugeint(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_hugeint;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The duckdb_uhugeint value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_uhugeint(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_uhugeint;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The duckdb_decimal value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_decimal(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_decimal;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The uint8_t value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_uint8(result: *mut duckdb_result, col: idx_t, row: idx_t) -> u8;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The uint16_t value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_uint16(result: *mut duckdb_result, col: idx_t, row: idx_t) -> u16;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The uint32_t value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_uint32(result: *mut duckdb_result, col: idx_t, row: idx_t) -> u32;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The uint64_t value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_uint64(result: *mut duckdb_result, col: idx_t, row: idx_t) -> u64;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The float value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_float(result: *mut duckdb_result, col: idx_t, row: idx_t) -> f32;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The double value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_double(result: *mut duckdb_result, col: idx_t, row: idx_t) -> f64;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The duckdb_date value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_date(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_date;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The duckdb_time value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_time(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_time;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The duckdb_timestamp value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_timestamp(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_timestamp;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The duckdb_interval value at the specified location, or 0 if the value cannot be converted."]
    pub fn duckdb_value_interval(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_interval;
}
unsafe extern "C" {
    #[doc = "DEPRECATED**: Use duckdb_value_string instead. This function does not work correctly if the string contains null\nbytes.\n\n @return The text value at the specified location as a null-terminated string, or nullptr if the value cannot be\nconverted. The result must be freed with `duckdb_free`."]
    pub fn duckdb_value_varchar(result: *mut duckdb_result, col: idx_t, row: idx_t) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nNo support for nested types, and for other complex types.\nThe resulting field \"string.data\" must be freed with `duckdb_free.`\n\n @return The string value at the specified location. Attempts to cast the result value to string."]
    pub fn duckdb_value_string(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_string;
}
unsafe extern "C" {
    #[doc = "DEPRECATED**: Use duckdb_value_string_internal instead. This function does not work correctly if the string contains\nnull bytes.\n\n @return The char* value at the specified location. ONLY works on VARCHAR columns and does not auto-cast.\nIf the column is NOT a VARCHAR column this function will return NULL.\n\nThe result must NOT be freed."]
    pub fn duckdb_value_varchar_internal(
        result: *mut duckdb_result,
        col: idx_t,
        row: idx_t,
    ) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "DEPRECATED**: Use duckdb_value_string_internal instead. This function does not work correctly if the string contains\nnull bytes.\n @return The char* value at the specified location. ONLY works on VARCHAR columns and does not auto-cast.\nIf the column is NOT a VARCHAR column this function will return NULL.\n\nThe result must NOT be freed."]
    pub fn duckdb_value_string_internal(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_string;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return The duckdb_blob value at the specified location. Returns a blob with blob.data set to nullptr if the\nvalue cannot be converted. The resulting field \"blob.data\" must be freed with `duckdb_free.`"]
    pub fn duckdb_value_blob(result: *mut duckdb_result, col: idx_t, row: idx_t) -> duckdb_blob;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\n @return Returns true if the value at the specified index is NULL, and false otherwise."]
    pub fn duckdb_value_is_null(result: *mut duckdb_result, col: idx_t, row: idx_t) -> bool;
}
unsafe extern "C" {
    #[doc = "Allocate `size` bytes of memory using the duckdb internal malloc function. Any memory allocated in this manner\nshould be freed using `duckdb_free`.\n\n @param size The number of bytes to allocate.\n @return A pointer to the allocated memory region."]
    pub fn duckdb_malloc(size: usize) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Free a value returned from `duckdb_malloc`, `duckdb_value_varchar`, `duckdb_value_blob`, or\n`duckdb_value_string`.\n\n @param ptr The memory region to de-allocate."]
    pub fn duckdb_free(ptr: *mut ::std::os::raw::c_void);
}
unsafe extern "C" {
    #[doc = "The internal vector size used by DuckDB.\nThis is the amount of tuples that will fit into a data chunk created by `duckdb_create_data_chunk`.\n\n @return The vector size."]
    pub fn duckdb_vector_size() -> idx_t;
}
unsafe extern "C" {
    #[doc = "Whether or not the duckdb_string_t value is inlined.\nThis means that the data of the string does not have a separate allocation."]
    pub fn duckdb_string_is_inlined(string: duckdb_string_t) -> bool;
}
unsafe extern "C" {
    #[doc = "Get the string length of a string_t\n\n @param string The string to get the length of.\n @return The length."]
    pub fn duckdb_string_t_length(string: duckdb_string_t) -> u32;
}
unsafe extern "C" {
    #[doc = "Get a pointer to the string data of a string_t\n\n @param string The string to get the pointer to.\n @return The pointer."]
    pub fn duckdb_string_t_data(string: *mut duckdb_string_t) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Decompose a `duckdb_date` object into year, month and date (stored as `duckdb_date_struct`).\n\n @param date The date object, as obtained from a `DUCKDB_TYPE_DATE` column.\n @return The `duckdb_date_struct` with the decomposed elements."]
    pub fn duckdb_from_date(date: duckdb_date) -> duckdb_date_struct;
}
unsafe extern "C" {
    #[doc = "Re-compose a `duckdb_date` from year, month and date (`duckdb_date_struct`).\n\n @param date The year, month and date stored in a `duckdb_date_struct`.\n @return The `duckdb_date` element."]
    pub fn duckdb_to_date(date: duckdb_date_struct) -> duckdb_date;
}
unsafe extern "C" {
    #[doc = "Test a `duckdb_date` to see if it is a finite value.\n\n @param date The date object, as obtained from a `DUCKDB_TYPE_DATE` column.\n @return True if the date is finite, false if it is ±infinity."]
    pub fn duckdb_is_finite_date(date: duckdb_date) -> bool;
}
unsafe extern "C" {
    #[doc = "Decompose a `duckdb_time` object into hour, minute, second and microsecond (stored as `duckdb_time_struct`).\n\n @param time The time object, as obtained from a `DUCKDB_TYPE_TIME` column.\n @return The `duckdb_time_struct` with the decomposed elements."]
    pub fn duckdb_from_time(time: duckdb_time) -> duckdb_time_struct;
}
unsafe extern "C" {
    #[doc = "Create a `duckdb_time_tz` object from micros and a timezone offset.\n\n @param micros The microsecond component of the time.\n @param offset The timezone offset component of the time.\n @return The `duckdb_time_tz` element."]
    pub fn duckdb_create_time_tz(micros: i64, offset: i32) -> duckdb_time_tz;
}
unsafe extern "C" {
    #[doc = "Decompose a TIME_TZ objects into micros and a timezone offset.\n\nUse `duckdb_from_time` to further decompose the micros into hour, minute, second and microsecond.\n\n @param micros The time object, as obtained from a `DUCKDB_TYPE_TIME_TZ` column."]
    pub fn duckdb_from_time_tz(micros: duckdb_time_tz) -> duckdb_time_tz_struct;
}
unsafe extern "C" {
    #[doc = "Re-compose a `duckdb_time` from hour, minute, second and microsecond (`duckdb_time_struct`).\n\n @param time The hour, minute, second and microsecond in a `duckdb_time_struct`.\n @return The `duckdb_time` element."]
    pub fn duckdb_to_time(time: duckdb_time_struct) -> duckdb_time;
}
unsafe extern "C" {
    #[doc = "Decompose a `duckdb_timestamp` object into a `duckdb_timestamp_struct`.\n\n @param ts The ts object, as obtained from a `DUCKDB_TYPE_TIMESTAMP` column.\n @return The `duckdb_timestamp_struct` with the decomposed elements."]
    pub fn duckdb_from_timestamp(ts: duckdb_timestamp) -> duckdb_timestamp_struct;
}
unsafe extern "C" {
    #[doc = "Re-compose a `duckdb_timestamp` from a duckdb_timestamp_struct.\n\n @param ts The de-composed elements in a `duckdb_timestamp_struct`.\n @return The `duckdb_timestamp` element."]
    pub fn duckdb_to_timestamp(ts: duckdb_timestamp_struct) -> duckdb_timestamp;
}
unsafe extern "C" {
    #[doc = "Test a `duckdb_timestamp` to see if it is a finite value.\n\n @param ts The duckdb_timestamp object, as obtained from a `DUCKDB_TYPE_TIMESTAMP` column.\n @return True if the timestamp is finite, false if it is ±infinity."]
    pub fn duckdb_is_finite_timestamp(ts: duckdb_timestamp) -> bool;
}
unsafe extern "C" {
    #[doc = "Test a `duckdb_timestamp_s` to see if it is a finite value.\n\n @param ts The duckdb_timestamp_s object, as obtained from a `DUCKDB_TYPE_TIMESTAMP_S` column.\n @return True if the timestamp is finite, false if it is ±infinity."]
    pub fn duckdb_is_finite_timestamp_s(ts: duckdb_timestamp_s) -> bool;
}
unsafe extern "C" {
    #[doc = "Test a `duckdb_timestamp_ms` to see if it is a finite value.\n\n @param ts The duckdb_timestamp_ms object, as obtained from a `DUCKDB_TYPE_TIMESTAMP_MS` column.\n @return True if the timestamp is finite, false if it is ±infinity."]
    pub fn duckdb_is_finite_timestamp_ms(ts: duckdb_timestamp_ms) -> bool;
}
unsafe extern "C" {
    #[doc = "Test a `duckdb_timestamp_ns` to see if it is a finite value.\n\n @param ts The duckdb_timestamp_ns object, as obtained from a `DUCKDB_TYPE_TIMESTAMP_NS` column.\n @return True if the timestamp is finite, false if it is ±infinity."]
    pub fn duckdb_is_finite_timestamp_ns(ts: duckdb_timestamp_ns) -> bool;
}
unsafe extern "C" {
    #[doc = "Converts a duckdb_hugeint object (as obtained from a `DUCKDB_TYPE_HUGEINT` column) into a double.\n\n @param val The hugeint value.\n @return The converted `double` element."]
    pub fn duckdb_hugeint_to_double(val: duckdb_hugeint) -> f64;
}
unsafe extern "C" {
    #[doc = "Converts a double value to a duckdb_hugeint object.\n\nIf the conversion fails because the double value is too big the result will be 0.\n\n @param val The double value.\n @return The converted `duckdb_hugeint` element."]
    pub fn duckdb_double_to_hugeint(val: f64) -> duckdb_hugeint;
}
unsafe extern "C" {
    #[doc = "Converts a duckdb_uhugeint object (as obtained from a `DUCKDB_TYPE_UHUGEINT` column) into a double.\n\n @param val The uhugeint value.\n @return The converted `double` element."]
    pub fn duckdb_uhugeint_to_double(val: duckdb_uhugeint) -> f64;
}
unsafe extern "C" {
    #[doc = "Converts a double value to a duckdb_uhugeint object.\n\nIf the conversion fails because the double value is too big the result will be 0.\n\n @param val The double value.\n @return The converted `duckdb_uhugeint` element."]
    pub fn duckdb_double_to_uhugeint(val: f64) -> duckdb_uhugeint;
}
unsafe extern "C" {
    #[doc = "Converts a double value to a duckdb_decimal object.\n\nIf the conversion fails because the double value is too big, or the width/scale are invalid the result will be 0.\n\n @param val The double value.\n @return The converted `duckdb_decimal` element."]
    pub fn duckdb_double_to_decimal(val: f64, width: u8, scale: u8) -> duckdb_decimal;
}
unsafe extern "C" {
    #[doc = "Converts a duckdb_decimal object (as obtained from a `DUCKDB_TYPE_DECIMAL` column) into a double.\n\n @param val The decimal value.\n @return The converted `double` element."]
    pub fn duckdb_decimal_to_double(val: duckdb_decimal) -> f64;
}
unsafe extern "C" {
    #[doc = "Create a prepared statement object from a query.\n\nNote that after calling `duckdb_prepare`, the prepared statement should always be destroyed using\n`duckdb_destroy_prepare`, even if the prepare fails.\n\nIf the prepare fails, `duckdb_prepare_error` can be called to obtain the reason why the prepare failed.\n\n @param connection The connection object\n @param query The SQL query to prepare\n @param out_prepared_statement The resulting prepared statement object\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_prepare(
        connection: duckdb_connection,
        query: *const ::std::os::raw::c_char,
        out_prepared_statement: *mut duckdb_prepared_statement,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Closes the prepared statement and de-allocates all memory allocated for the statement.\n\n @param prepared_statement The prepared statement to destroy."]
    pub fn duckdb_destroy_prepare(prepared_statement: *mut duckdb_prepared_statement);
}
unsafe extern "C" {
    #[doc = "Returns the error message associated with the given prepared statement.\nIf the prepared statement has no error message, this returns `nullptr` instead.\n\nThe error message should not be freed. It will be de-allocated when `duckdb_destroy_prepare` is called.\n\n @param prepared_statement The prepared statement to obtain the error from.\n @return The error message, or `nullptr` if there is none."]
    pub fn duckdb_prepare_error(prepared_statement: duckdb_prepared_statement) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Returns the number of parameters that can be provided to the given prepared statement.\n\nReturns 0 if the query was not successfully prepared.\n\n @param prepared_statement The prepared statement to obtain the number of parameters for."]
    pub fn duckdb_nparams(prepared_statement: duckdb_prepared_statement) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Returns the name used to identify the parameter\nThe returned string should be freed using `duckdb_free`.\n\nReturns NULL if the index is out of range for the provided prepared statement.\n\n @param prepared_statement The prepared statement for which to get the parameter name from."]
    pub fn duckdb_parameter_name(
        prepared_statement: duckdb_prepared_statement,
        index: idx_t,
    ) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Returns the parameter type for the parameter at the given index.\n\nReturns `DUCKDB_TYPE_INVALID` if the parameter index is out of range or the statement was not successfully prepared.\n\n @param prepared_statement The prepared statement.\n @param param_idx The parameter index.\n @return The parameter type"]
    pub fn duckdb_param_type(prepared_statement: duckdb_prepared_statement, param_idx: idx_t) -> duckdb_type;
}
unsafe extern "C" {
    #[doc = "Returns the logical type for the parameter at the given index.\n\nReturns `nullptr` if the parameter index is out of range or the statement was not successfully prepared.\n\nThe return type of this call should be destroyed with `duckdb_destroy_logical_type`.\n\n @param prepared_statement The prepared statement.\n @param param_idx The parameter index.\n @return The logical type of the parameter"]
    pub fn duckdb_param_logical_type(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
    ) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Clear the params bind to the prepared statement."]
    pub fn duckdb_clear_bindings(prepared_statement: duckdb_prepared_statement) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Returns the statement type of the statement to be executed\n\n @param statement The prepared statement.\n @return duckdb_statement_type value or DUCKDB_STATEMENT_TYPE_INVALID"]
    pub fn duckdb_prepared_statement_type(statement: duckdb_prepared_statement) -> duckdb_statement_type;
}
unsafe extern "C" {
    #[doc = "Binds a value to the prepared statement at the specified index."]
    pub fn duckdb_bind_value(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_value,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Retrieve the index of the parameter for the prepared statement, identified by name"]
    pub fn duckdb_bind_parameter_index(
        prepared_statement: duckdb_prepared_statement,
        param_idx_out: *mut idx_t,
        name: *const ::std::os::raw::c_char,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a bool value to the prepared statement at the specified index."]
    pub fn duckdb_bind_boolean(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: bool,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds an int8_t value to the prepared statement at the specified index."]
    pub fn duckdb_bind_int8(prepared_statement: duckdb_prepared_statement, param_idx: idx_t, val: i8) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds an int16_t value to the prepared statement at the specified index."]
    pub fn duckdb_bind_int16(prepared_statement: duckdb_prepared_statement, param_idx: idx_t, val: i16)
        -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds an int32_t value to the prepared statement at the specified index."]
    pub fn duckdb_bind_int32(prepared_statement: duckdb_prepared_statement, param_idx: idx_t, val: i32)
        -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds an int64_t value to the prepared statement at the specified index."]
    pub fn duckdb_bind_int64(prepared_statement: duckdb_prepared_statement, param_idx: idx_t, val: i64)
        -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a duckdb_hugeint value to the prepared statement at the specified index."]
    pub fn duckdb_bind_hugeint(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_hugeint,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a duckdb_uhugeint value to the prepared statement at the specified index."]
    pub fn duckdb_bind_uhugeint(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_uhugeint,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a duckdb_decimal value to the prepared statement at the specified index."]
    pub fn duckdb_bind_decimal(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_decimal,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a uint8_t value to the prepared statement at the specified index."]
    pub fn duckdb_bind_uint8(prepared_statement: duckdb_prepared_statement, param_idx: idx_t, val: u8) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a uint16_t value to the prepared statement at the specified index."]
    pub fn duckdb_bind_uint16(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: u16,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a uint32_t value to the prepared statement at the specified index."]
    pub fn duckdb_bind_uint32(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: u32,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a uint64_t value to the prepared statement at the specified index."]
    pub fn duckdb_bind_uint64(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: u64,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a float value to the prepared statement at the specified index."]
    pub fn duckdb_bind_float(prepared_statement: duckdb_prepared_statement, param_idx: idx_t, val: f32)
        -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a double value to the prepared statement at the specified index."]
    pub fn duckdb_bind_double(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: f64,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a duckdb_date value to the prepared statement at the specified index."]
    pub fn duckdb_bind_date(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_date,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a duckdb_time value to the prepared statement at the specified index."]
    pub fn duckdb_bind_time(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_time,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a duckdb_timestamp value to the prepared statement at the specified index."]
    pub fn duckdb_bind_timestamp(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_timestamp,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a duckdb_timestamp value to the prepared statement at the specified index."]
    pub fn duckdb_bind_timestamp_tz(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_timestamp,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a duckdb_interval value to the prepared statement at the specified index."]
    pub fn duckdb_bind_interval(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: duckdb_interval,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a null-terminated varchar value to the prepared statement at the specified index."]
    pub fn duckdb_bind_varchar(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: *const ::std::os::raw::c_char,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a varchar value to the prepared statement at the specified index."]
    pub fn duckdb_bind_varchar_length(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        val: *const ::std::os::raw::c_char,
        length: idx_t,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a blob value to the prepared statement at the specified index."]
    pub fn duckdb_bind_blob(
        prepared_statement: duckdb_prepared_statement,
        param_idx: idx_t,
        data: *const ::std::os::raw::c_void,
        length: idx_t,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Binds a NULL value to the prepared statement at the specified index."]
    pub fn duckdb_bind_null(prepared_statement: duckdb_prepared_statement, param_idx: idx_t) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Executes the prepared statement with the given bound parameters, and returns a materialized query result.\n\nThis method can be called multiple times for each prepared statement, and the parameters can be modified\nbetween calls to this function.\n\nNote that the result must be freed with `duckdb_destroy_result`.\n\n @param prepared_statement The prepared statement to execute.\n @param out_result The query result.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_execute_prepared(
        prepared_statement: duckdb_prepared_statement,
        out_result: *mut duckdb_result,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nExecutes the prepared statement with the given bound parameters, and returns an optionally-streaming query result.\nTo determine if the resulting query was in fact streamed, use `duckdb_result_is_streaming`\n\nThis method can be called multiple times for each prepared statement, and the parameters can be modified\nbetween calls to this function.\n\nNote that the result must be freed with `duckdb_destroy_result`.\n\n @param prepared_statement The prepared statement to execute.\n @param out_result The query result.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_execute_prepared_streaming(
        prepared_statement: duckdb_prepared_statement,
        out_result: *mut duckdb_result,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Extract all statements from a query.\nNote that after calling `duckdb_extract_statements`, the extracted statements should always be destroyed using\n`duckdb_destroy_extracted`, even if no statements were extracted.\n\nIf the extract fails, `duckdb_extract_statements_error` can be called to obtain the reason why the extract failed.\n\n @param connection The connection object\n @param query The SQL query to extract\n @param out_extracted_statements The resulting extracted statements object\n @return The number of extracted statements or 0 on failure."]
    pub fn duckdb_extract_statements(
        connection: duckdb_connection,
        query: *const ::std::os::raw::c_char,
        out_extracted_statements: *mut duckdb_extracted_statements,
    ) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Prepare an extracted statement.\nNote that after calling `duckdb_prepare_extracted_statement`, the prepared statement should always be destroyed using\n`duckdb_destroy_prepare`, even if the prepare fails.\n\nIf the prepare fails, `duckdb_prepare_error` can be called to obtain the reason why the prepare failed.\n\n @param connection The connection object\n @param extracted_statements The extracted statements object\n @param index The index of the extracted statement to prepare\n @param out_prepared_statement The resulting prepared statement object\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_prepare_extracted_statement(
        connection: duckdb_connection,
        extracted_statements: duckdb_extracted_statements,
        index: idx_t,
        out_prepared_statement: *mut duckdb_prepared_statement,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Returns the error message contained within the extracted statements.\nThe result of this function must not be freed. It will be cleaned up when `duckdb_destroy_extracted` is called.\n\n @param extracted_statements The extracted statements to fetch the error from.\n @return The error of the extracted statements."]
    pub fn duckdb_extract_statements_error(
        extracted_statements: duckdb_extracted_statements,
    ) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "De-allocates all memory allocated for the extracted statements.\n @param extracted_statements The extracted statements to destroy."]
    pub fn duckdb_destroy_extracted(extracted_statements: *mut duckdb_extracted_statements);
}
unsafe extern "C" {
    #[doc = "Executes the prepared statement with the given bound parameters, and returns a pending result.\nThe pending result represents an intermediate structure for a query that is not yet fully executed.\nThe pending result can be used to incrementally execute a query, returning control to the client between tasks.\n\nNote that after calling `duckdb_pending_prepared`, the pending result should always be destroyed using\n`duckdb_destroy_pending`, even if this function returns DuckDBError.\n\n @param prepared_statement The prepared statement to execute.\n @param out_result The pending query result.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_pending_prepared(
        prepared_statement: duckdb_prepared_statement,
        out_result: *mut duckdb_pending_result,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nExecutes the prepared statement with the given bound parameters, and returns a pending result.\nThis pending result will create a streaming duckdb_result when executed.\nThe pending result represents an intermediate structure for a query that is not yet fully executed.\n\nNote that after calling `duckdb_pending_prepared_streaming`, the pending result should always be destroyed using\n`duckdb_destroy_pending`, even if this function returns DuckDBError.\n\n @param prepared_statement The prepared statement to execute.\n @param out_result The pending query result.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_pending_prepared_streaming(
        prepared_statement: duckdb_prepared_statement,
        out_result: *mut duckdb_pending_result,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Closes the pending result and de-allocates all memory allocated for the result.\n\n @param pending_result The pending result to destroy."]
    pub fn duckdb_destroy_pending(pending_result: *mut duckdb_pending_result);
}
unsafe extern "C" {
    #[doc = "Returns the error message contained within the pending result.\n\nThe result of this function must not be freed. It will be cleaned up when `duckdb_destroy_pending` is called.\n\n @param pending_result The pending result to fetch the error from.\n @return The error of the pending result."]
    pub fn duckdb_pending_error(pending_result: duckdb_pending_result) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Executes a single task within the query, returning whether or not the query is ready.\n\nIf this returns DUCKDB_PENDING_RESULT_READY, the duckdb_execute_pending function can be called to obtain the result.\nIf this returns DUCKDB_PENDING_RESULT_NOT_READY, the duckdb_pending_execute_task function should be called again.\nIf this returns DUCKDB_PENDING_ERROR, an error occurred during execution.\n\nThe error message can be obtained by calling duckdb_pending_error on the pending_result.\n\n @param pending_result The pending result to execute a task within.\n @return The state of the pending result after the execution."]
    pub fn duckdb_pending_execute_task(pending_result: duckdb_pending_result) -> duckdb_pending_state;
}
unsafe extern "C" {
    #[doc = "If this returns DUCKDB_PENDING_RESULT_READY, the duckdb_execute_pending function can be called to obtain the result.\nIf this returns DUCKDB_PENDING_RESULT_NOT_READY, the duckdb_pending_execute_check_state function should be called again.\nIf this returns DUCKDB_PENDING_ERROR, an error occurred during execution.\n\nThe error message can be obtained by calling duckdb_pending_error on the pending_result.\n\n @param pending_result The pending result.\n @return The state of the pending result."]
    pub fn duckdb_pending_execute_check_state(pending_result: duckdb_pending_result) -> duckdb_pending_state;
}
unsafe extern "C" {
    #[doc = "Fully execute a pending query result, returning the final query result.\n\nIf duckdb_pending_execute_task has been called until DUCKDB_PENDING_RESULT_READY was returned, this will return fast.\nOtherwise, all remaining tasks must be executed first.\n\nNote that the result must be freed with `duckdb_destroy_result`.\n\n @param pending_result The pending result to execute.\n @param out_result The result object.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_execute_pending(
        pending_result: duckdb_pending_result,
        out_result: *mut duckdb_result,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Returns whether a duckdb_pending_state is finished executing. For example if `pending_state` is\nDUCKDB_PENDING_RESULT_READY, this function will return true.\n\n @param pending_state The pending state on which to decide whether to finish execution.\n @return Boolean indicating pending execution should be considered finished."]
    pub fn duckdb_pending_execution_is_finished(pending_state: duckdb_pending_state) -> bool;
}
unsafe extern "C" {
    #[doc = "Destroys the value and de-allocates all memory allocated for that type.\n\n @param value The value to destroy."]
    pub fn duckdb_destroy_value(value: *mut duckdb_value);
}
unsafe extern "C" {
    #[doc = "Creates a value from a null-terminated string\n\n @param text The null-terminated string\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_varchar(text: *const ::std::os::raw::c_char) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a string\n\n @param text The text\n @param length The length of the text\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_varchar_length(text: *const ::std::os::raw::c_char, length: idx_t) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a boolean\n\n @param input The boolean value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_bool(input: bool) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from an int8_t (a tinyint)\n\n @param input The tinyint value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_int8(input: i8) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a uint8_t (a utinyint)\n\n @param input The utinyint value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_uint8(input: u8) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from an int16_t (a smallint)\n\n @param input The smallint value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_int16(input: i16) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a uint16_t (a usmallint)\n\n @param input The usmallint value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_uint16(input: u16) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from an int32_t (an integer)\n\n @param input The integer value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_int32(input: i32) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a uint32_t (a uinteger)\n\n @param input The uinteger value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_uint32(input: u32) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a uint64_t (a ubigint)\n\n @param input The ubigint value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_uint64(input: u64) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from an int64\n\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_int64(val: i64) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a hugeint\n\n @param input The hugeint value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_hugeint(input: duckdb_hugeint) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a uhugeint\n\n @param input The uhugeint value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_uhugeint(input: duckdb_uhugeint) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a VARINT value from a duckdb_varint\n\n @param input The duckdb_varint value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_varint(input: duckdb_varint) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a DECIMAL value from a duckdb_decimal\n\n @param input The duckdb_decimal value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_decimal(input: duckdb_decimal) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a float\n\n @param input The float value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_float(input: f32) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a double\n\n @param input The double value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_double(input: f64) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a date\n\n @param input The date value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_date(input: duckdb_date) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a time\n\n @param input The time value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_time(input: duckdb_time) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a time_tz.\nNot to be confused with `duckdb_create_time_tz`, which creates a duckdb_time_tz_t.\n\n @param value The time_tz value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_time_tz_value(value: duckdb_time_tz) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a TIMESTAMP value from a duckdb_timestamp\n\n @param input The duckdb_timestamp value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_timestamp(input: duckdb_timestamp) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a TIMESTAMP_TZ value from a duckdb_timestamp\n\n @param input The duckdb_timestamp value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_timestamp_tz(input: duckdb_timestamp) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a TIMESTAMP_S value from a duckdb_timestamp_s\n\n @param input The duckdb_timestamp_s value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_timestamp_s(input: duckdb_timestamp_s) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a TIMESTAMP_MS value from a duckdb_timestamp_ms\n\n @param input The duckdb_timestamp_ms value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_timestamp_ms(input: duckdb_timestamp_ms) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a TIMESTAMP_NS value from a duckdb_timestamp_ns\n\n @param input The duckdb_timestamp_ns value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_timestamp_ns(input: duckdb_timestamp_ns) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from an interval\n\n @param input The interval value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_interval(input: duckdb_interval) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a value from a blob\n\n @param data The blob data\n @param length The length of the blob data\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_blob(data: *const u8, length: idx_t) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a BIT value from a duckdb_bit\n\n @param input The duckdb_bit value\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_bit(input: duckdb_bit) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a UUID value from a uhugeint\n\n @param input The duckdb_uhugeint containing the UUID\n @return The value. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_uuid(input: duckdb_uhugeint) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns the boolean value of the given value.\n\n @param val A duckdb_value containing a boolean\n @return A boolean, or false if the value cannot be converted"]
    pub fn duckdb_get_bool(val: duckdb_value) -> bool;
}
unsafe extern "C" {
    #[doc = "Returns the int8_t value of the given value.\n\n @param val A duckdb_value containing a tinyint\n @return A int8_t, or MinValue<int8> if the value cannot be converted"]
    pub fn duckdb_get_int8(val: duckdb_value) -> i8;
}
unsafe extern "C" {
    #[doc = "Returns the uint8_t value of the given value.\n\n @param val A duckdb_value containing a utinyint\n @return A uint8_t, or MinValue<uint8> if the value cannot be converted"]
    pub fn duckdb_get_uint8(val: duckdb_value) -> u8;
}
unsafe extern "C" {
    #[doc = "Returns the int16_t value of the given value.\n\n @param val A duckdb_value containing a smallint\n @return A int16_t, or MinValue<int16> if the value cannot be converted"]
    pub fn duckdb_get_int16(val: duckdb_value) -> i16;
}
unsafe extern "C" {
    #[doc = "Returns the uint16_t value of the given value.\n\n @param val A duckdb_value containing a usmallint\n @return A uint16_t, or MinValue<uint16> if the value cannot be converted"]
    pub fn duckdb_get_uint16(val: duckdb_value) -> u16;
}
unsafe extern "C" {
    #[doc = "Returns the int32_t value of the given value.\n\n @param val A duckdb_value containing an integer\n @return A int32_t, or MinValue<int32> if the value cannot be converted"]
    pub fn duckdb_get_int32(val: duckdb_value) -> i32;
}
unsafe extern "C" {
    #[doc = "Returns the uint32_t value of the given value.\n\n @param val A duckdb_value containing a uinteger\n @return A uint32_t, or MinValue<uint32> if the value cannot be converted"]
    pub fn duckdb_get_uint32(val: duckdb_value) -> u32;
}
unsafe extern "C" {
    #[doc = "Returns the int64_t value of the given value.\n\n @param val A duckdb_value containing a bigint\n @return A int64_t, or MinValue<int64> if the value cannot be converted"]
    pub fn duckdb_get_int64(val: duckdb_value) -> i64;
}
unsafe extern "C" {
    #[doc = "Returns the uint64_t value of the given value.\n\n @param val A duckdb_value containing a ubigint\n @return A uint64_t, or MinValue<uint64> if the value cannot be converted"]
    pub fn duckdb_get_uint64(val: duckdb_value) -> u64;
}
unsafe extern "C" {
    #[doc = "Returns the hugeint value of the given value.\n\n @param val A duckdb_value containing a hugeint\n @return A duckdb_hugeint, or MinValue<hugeint> if the value cannot be converted"]
    pub fn duckdb_get_hugeint(val: duckdb_value) -> duckdb_hugeint;
}
unsafe extern "C" {
    #[doc = "Returns the uhugeint value of the given value.\n\n @param val A duckdb_value containing a uhugeint\n @return A duckdb_uhugeint, or MinValue<uhugeint> if the value cannot be converted"]
    pub fn duckdb_get_uhugeint(val: duckdb_value) -> duckdb_uhugeint;
}
unsafe extern "C" {
    #[doc = "Returns the duckdb_varint value of the given value.\nThe `data` field must be destroyed with `duckdb_free`.\n\n @param val A duckdb_value containing a VARINT\n @return A duckdb_varint. The `data` field must be destroyed with `duckdb_free`."]
    pub fn duckdb_get_varint(val: duckdb_value) -> duckdb_varint;
}
unsafe extern "C" {
    #[doc = "Returns the duckdb_decimal value of the given value.\n\n @param val A duckdb_value containing a DECIMAL\n @return A duckdb_decimal, or MinValue<decimal> if the value cannot be converted"]
    pub fn duckdb_get_decimal(val: duckdb_value) -> duckdb_decimal;
}
unsafe extern "C" {
    #[doc = "Returns the float value of the given value.\n\n @param val A duckdb_value containing a float\n @return A float, or NAN if the value cannot be converted"]
    pub fn duckdb_get_float(val: duckdb_value) -> f32;
}
unsafe extern "C" {
    #[doc = "Returns the double value of the given value.\n\n @param val A duckdb_value containing a double\n @return A double, or NAN if the value cannot be converted"]
    pub fn duckdb_get_double(val: duckdb_value) -> f64;
}
unsafe extern "C" {
    #[doc = "Returns the date value of the given value.\n\n @param val A duckdb_value containing a date\n @return A duckdb_date, or MinValue<date> if the value cannot be converted"]
    pub fn duckdb_get_date(val: duckdb_value) -> duckdb_date;
}
unsafe extern "C" {
    #[doc = "Returns the time value of the given value.\n\n @param val A duckdb_value containing a time\n @return A duckdb_time, or MinValue<time> if the value cannot be converted"]
    pub fn duckdb_get_time(val: duckdb_value) -> duckdb_time;
}
unsafe extern "C" {
    #[doc = "Returns the time_tz value of the given value.\n\n @param val A duckdb_value containing a time_tz\n @return A duckdb_time_tz, or MinValue<time_tz> if the value cannot be converted"]
    pub fn duckdb_get_time_tz(val: duckdb_value) -> duckdb_time_tz;
}
unsafe extern "C" {
    #[doc = "Returns the TIMESTAMP value of the given value.\n\n @param val A duckdb_value containing a TIMESTAMP\n @return A duckdb_timestamp, or MinValue<timestamp> if the value cannot be converted"]
    pub fn duckdb_get_timestamp(val: duckdb_value) -> duckdb_timestamp;
}
unsafe extern "C" {
    #[doc = "Returns the TIMESTAMP_TZ value of the given value.\n\n @param val A duckdb_value containing a TIMESTAMP_TZ\n @return A duckdb_timestamp, or MinValue<timestamp_tz> if the value cannot be converted"]
    pub fn duckdb_get_timestamp_tz(val: duckdb_value) -> duckdb_timestamp;
}
unsafe extern "C" {
    #[doc = "Returns the duckdb_timestamp_s value of the given value.\n\n @param val A duckdb_value containing a TIMESTAMP_S\n @return A duckdb_timestamp_s, or MinValue<timestamp_s> if the value cannot be converted"]
    pub fn duckdb_get_timestamp_s(val: duckdb_value) -> duckdb_timestamp_s;
}
unsafe extern "C" {
    #[doc = "Returns the duckdb_timestamp_ms value of the given value.\n\n @param val A duckdb_value containing a TIMESTAMP_MS\n @return A duckdb_timestamp_ms, or MinValue<timestamp_ms> if the value cannot be converted"]
    pub fn duckdb_get_timestamp_ms(val: duckdb_value) -> duckdb_timestamp_ms;
}
unsafe extern "C" {
    #[doc = "Returns the duckdb_timestamp_ns value of the given value.\n\n @param val A duckdb_value containing a TIMESTAMP_NS\n @return A duckdb_timestamp_ns, or MinValue<timestamp_ns> if the value cannot be converted"]
    pub fn duckdb_get_timestamp_ns(val: duckdb_value) -> duckdb_timestamp_ns;
}
unsafe extern "C" {
    #[doc = "Returns the interval value of the given value.\n\n @param val A duckdb_value containing a interval\n @return A duckdb_interval, or MinValue<interval> if the value cannot be converted"]
    pub fn duckdb_get_interval(val: duckdb_value) -> duckdb_interval;
}
unsafe extern "C" {
    #[doc = "Returns the type of the given value. The type is valid as long as the value is not destroyed.\nThe type itself must not be destroyed.\n\n @param val A duckdb_value\n @return A duckdb_logical_type."]
    pub fn duckdb_get_value_type(val: duckdb_value) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Returns the blob value of the given value.\n\n @param val A duckdb_value containing a blob\n @return A duckdb_blob"]
    pub fn duckdb_get_blob(val: duckdb_value) -> duckdb_blob;
}
unsafe extern "C" {
    #[doc = "Returns the duckdb_bit value of the given value.\nThe `data` field must be destroyed with `duckdb_free`.\n\n @param val A duckdb_value containing a BIT\n @return A duckdb_bit"]
    pub fn duckdb_get_bit(val: duckdb_value) -> duckdb_bit;
}
unsafe extern "C" {
    #[doc = "Returns a duckdb_uhugeint representing the UUID value of the given value.\n\n @param val A duckdb_value containing a UUID\n @return A duckdb_uhugeint representing the UUID value"]
    pub fn duckdb_get_uuid(val: duckdb_value) -> duckdb_uhugeint;
}
unsafe extern "C" {
    #[doc = "Obtains a string representation of the given value.\nThe result must be destroyed with `duckdb_free`.\n\n @param value The value\n @return The string value. This must be destroyed with `duckdb_free`."]
    pub fn duckdb_get_varchar(value: duckdb_value) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Creates a struct value from a type and an array of values. Must be destroyed with `duckdb_destroy_value`.\n\n @param type The type of the struct\n @param values The values for the struct fields\n @return The struct value, or nullptr, if any child type is `DUCKDB_TYPE_ANY` or `DUCKDB_TYPE_INVALID`."]
    pub fn duckdb_create_struct_value(type_: duckdb_logical_type, values: *mut duckdb_value) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a list value from a child (element) type and an array of values of length `value_count`.\nMust be destroyed with `duckdb_destroy_value`.\n\n @param type The type of the list\n @param values The values for the list\n @param value_count The number of values in the list\n @return The list value, or nullptr, if the child type is `DUCKDB_TYPE_ANY` or `DUCKDB_TYPE_INVALID`."]
    pub fn duckdb_create_list_value(
        type_: duckdb_logical_type,
        values: *mut duckdb_value,
        value_count: idx_t,
    ) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates an array value from a child (element) type and an array of values of length `value_count`.\nMust be destroyed with `duckdb_destroy_value`.\n\n @param type The type of the array\n @param values The values for the array\n @param value_count The number of values in the array\n @return The array value, or nullptr, if the child type is `DUCKDB_TYPE_ANY` or `DUCKDB_TYPE_INVALID`."]
    pub fn duckdb_create_array_value(
        type_: duckdb_logical_type,
        values: *mut duckdb_value,
        value_count: idx_t,
    ) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a map value from a map type and two arrays, one for the keys and one for the values, each of length\n`entry_count`. Must be destroyed with `duckdb_destroy_value`.\n\n @param map_type The map type\n @param keys The keys of the map\n @param values The values of the map\n @param entry_count The number of entrys (key-value pairs) in the map\n @return The map value, or nullptr, if the parameters are invalid."]
    pub fn duckdb_create_map_value(
        map_type: duckdb_logical_type,
        keys: *mut duckdb_value,
        values: *mut duckdb_value,
        entry_count: idx_t,
    ) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates a union value from a union type, a tag index, and a value.\nMust be destroyed with `duckdb_destroy_value`.\n\n @param union_type The union type\n @param tag_index The index of the tag of the union\n @param value The value of the union\n @return The union value, or nullptr, if the parameters are invalid."]
    pub fn duckdb_create_union_value(
        union_type: duckdb_logical_type,
        tag_index: idx_t,
        value: duckdb_value,
    ) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns the number of elements in a MAP value.\n\n @param value The MAP value.\n @return The number of elements in the map."]
    pub fn duckdb_get_map_size(value: duckdb_value) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Returns the MAP key at index as a duckdb_value.\n\n @param value The MAP value.\n @param index The index of the key.\n @return The key as a duckdb_value."]
    pub fn duckdb_get_map_key(value: duckdb_value, index: idx_t) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns the MAP value at index as a duckdb_value.\n\n @param value The MAP value.\n @param index The index of the value.\n @return The value as a duckdb_value."]
    pub fn duckdb_get_map_value(value: duckdb_value, index: idx_t) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns whether the value's type is SQLNULL or not.\n\n @param value The value to check.\n @return True, if the value's type is SQLNULL, otherwise false."]
    pub fn duckdb_is_null_value(value: duckdb_value) -> bool;
}
unsafe extern "C" {
    #[doc = "Creates a value of type SQLNULL.\n\n @return The duckdb_value representing SQLNULL. This must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_create_null_value() -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns the number of elements in a LIST value.\n\n @param value The LIST value.\n @return The number of elements in the list."]
    pub fn duckdb_get_list_size(value: duckdb_value) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Returns the LIST child at index as a duckdb_value.\n\n @param value The LIST value.\n @param index The index of the child.\n @return The child as a duckdb_value."]
    pub fn duckdb_get_list_child(value: duckdb_value, index: idx_t) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Creates an enum value from a type and a value. Must be destroyed with `duckdb_destroy_value`.\n\n @param type The type of the enum\n @param value The value for the enum\n @return The enum value, or nullptr."]
    pub fn duckdb_create_enum_value(type_: duckdb_logical_type, value: u64) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns the enum value of the given value.\n\n @param value A duckdb_value containing an enum\n @return A uint64_t, or MinValue<uint64> if the value cannot be converted"]
    pub fn duckdb_get_enum_value(value: duckdb_value) -> u64;
}
unsafe extern "C" {
    #[doc = "Returns the STRUCT child at index as a duckdb_value.\n\n @param value The STRUCT value.\n @param index The index of the child.\n @return The child as a duckdb_value."]
    pub fn duckdb_get_struct_child(value: duckdb_value, index: idx_t) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns the SQL string representation of the given value.\n\n @param value A duckdb_value.\n @return The SQL string representation as a null-terminated string. The result must be freed with `duckdb_free`."]
    pub fn duckdb_value_to_string(value: duckdb_value) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Creates a `duckdb_logical_type` from a primitive type.\nThe resulting logical type must be destroyed with `duckdb_destroy_logical_type`.\n\nReturns an invalid logical type, if type is: `DUCKDB_TYPE_INVALID`, `DUCKDB_TYPE_DECIMAL`, `DUCKDB_TYPE_ENUM`,\n`DUCKDB_TYPE_LIST`, `DUCKDB_TYPE_STRUCT`, `DUCKDB_TYPE_MAP`, `DUCKDB_TYPE_ARRAY`, or `DUCKDB_TYPE_UNION`.\n\n @param type The primitive type to create.\n @return The logical type."]
    pub fn duckdb_create_logical_type(type_: duckdb_type) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Returns the alias of a duckdb_logical_type, if set, else `nullptr`.\nThe result must be destroyed with `duckdb_free`.\n\n @param type The logical type\n @return The alias or `nullptr`"]
    pub fn duckdb_logical_type_get_alias(type_: duckdb_logical_type) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Sets the alias of a duckdb_logical_type.\n\n @param type The logical type\n @param alias The alias to set"]
    pub fn duckdb_logical_type_set_alias(type_: duckdb_logical_type, alias: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Creates a LIST type from its child type.\nThe return type must be destroyed with `duckdb_destroy_logical_type`.\n\n @param type The child type of the list\n @return The logical type."]
    pub fn duckdb_create_list_type(type_: duckdb_logical_type) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Creates an ARRAY type from its child type.\nThe return type must be destroyed with `duckdb_destroy_logical_type`.\n\n @param type The child type of the array.\n @param array_size The number of elements in the array.\n @return The logical type."]
    pub fn duckdb_create_array_type(type_: duckdb_logical_type, array_size: idx_t) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Creates a MAP type from its key type and value type.\nThe return type must be destroyed with `duckdb_destroy_logical_type`.\n\n @param key_type The map's key type.\n @param value_type The map's value type.\n @return The logical type."]
    pub fn duckdb_create_map_type(
        key_type: duckdb_logical_type,
        value_type: duckdb_logical_type,
    ) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Creates a UNION type from the passed arrays.\nThe return type must be destroyed with `duckdb_destroy_logical_type`.\n\n @param member_types The array of union member types.\n @param member_names The union member names.\n @param member_count The number of union members.\n @return The logical type."]
    pub fn duckdb_create_union_type(
        member_types: *mut duckdb_logical_type,
        member_names: *mut *const ::std::os::raw::c_char,
        member_count: idx_t,
    ) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Creates a STRUCT type based on the member types and names.\nThe resulting type must be destroyed with `duckdb_destroy_logical_type`.\n\n @param member_types The array of types of the struct members.\n @param member_names The array of names of the struct members.\n @param member_count The number of members of the struct.\n @return The logical type."]
    pub fn duckdb_create_struct_type(
        member_types: *mut duckdb_logical_type,
        member_names: *mut *const ::std::os::raw::c_char,
        member_count: idx_t,
    ) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Creates an ENUM type from the passed member name array.\nThe resulting type should be destroyed with `duckdb_destroy_logical_type`.\n\n @param member_names The array of names that the enum should consist of.\n @param member_count The number of elements that were specified in the array.\n @return The logical type."]
    pub fn duckdb_create_enum_type(
        member_names: *mut *const ::std::os::raw::c_char,
        member_count: idx_t,
    ) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Creates a DECIMAL type with the specified width and scale.\nThe resulting type should be destroyed with `duckdb_destroy_logical_type`.\n\n @param width The width of the decimal type\n @param scale The scale of the decimal type\n @return The logical type."]
    pub fn duckdb_create_decimal_type(width: u8, scale: u8) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Retrieves the enum `duckdb_type` of a `duckdb_logical_type`.\n\n @param type The logical type.\n @return The `duckdb_type` id."]
    pub fn duckdb_get_type_id(type_: duckdb_logical_type) -> duckdb_type;
}
unsafe extern "C" {
    #[doc = "Retrieves the width of a decimal type.\n\n @param type The logical type object\n @return The width of the decimal type"]
    pub fn duckdb_decimal_width(type_: duckdb_logical_type) -> u8;
}
unsafe extern "C" {
    #[doc = "Retrieves the scale of a decimal type.\n\n @param type The logical type object\n @return The scale of the decimal type"]
    pub fn duckdb_decimal_scale(type_: duckdb_logical_type) -> u8;
}
unsafe extern "C" {
    #[doc = "Retrieves the internal storage type of a decimal type.\n\n @param type The logical type object\n @return The internal type of the decimal type"]
    pub fn duckdb_decimal_internal_type(type_: duckdb_logical_type) -> duckdb_type;
}
unsafe extern "C" {
    #[doc = "Retrieves the internal storage type of an enum type.\n\n @param type The logical type object\n @return The internal type of the enum type"]
    pub fn duckdb_enum_internal_type(type_: duckdb_logical_type) -> duckdb_type;
}
unsafe extern "C" {
    #[doc = "Retrieves the dictionary size of the enum type.\n\n @param type The logical type object\n @return The dictionary size of the enum type"]
    pub fn duckdb_enum_dictionary_size(type_: duckdb_logical_type) -> u32;
}
unsafe extern "C" {
    #[doc = "Retrieves the dictionary value at the specified position from the enum.\n\nThe result must be freed with `duckdb_free`.\n\n @param type The logical type object\n @param index The index in the dictionary\n @return The string value of the enum type. Must be freed with `duckdb_free`."]
    pub fn duckdb_enum_dictionary_value(type_: duckdb_logical_type, index: idx_t) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Retrieves the child type of the given LIST type. Also accepts MAP types.\nThe result must be freed with `duckdb_destroy_logical_type`.\n\n @param type The logical type, either LIST or MAP.\n @return The child type of the LIST or MAP type."]
    pub fn duckdb_list_type_child_type(type_: duckdb_logical_type) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Retrieves the child type of the given ARRAY type.\n\nThe result must be freed with `duckdb_destroy_logical_type`.\n\n @param type The logical type. Must be ARRAY.\n @return The child type of the ARRAY type."]
    pub fn duckdb_array_type_child_type(type_: duckdb_logical_type) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Retrieves the array size of the given array type.\n\n @param type The logical type object\n @return The fixed number of elements the values of this array type can store."]
    pub fn duckdb_array_type_array_size(type_: duckdb_logical_type) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Retrieves the key type of the given map type.\n\nThe result must be freed with `duckdb_destroy_logical_type`.\n\n @param type The logical type object\n @return The key type of the map type. Must be destroyed with `duckdb_destroy_logical_type`."]
    pub fn duckdb_map_type_key_type(type_: duckdb_logical_type) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Retrieves the value type of the given map type.\n\nThe result must be freed with `duckdb_destroy_logical_type`.\n\n @param type The logical type object\n @return The value type of the map type. Must be destroyed with `duckdb_destroy_logical_type`."]
    pub fn duckdb_map_type_value_type(type_: duckdb_logical_type) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Returns the number of children of a struct type.\n\n @param type The logical type object\n @return The number of children of a struct type."]
    pub fn duckdb_struct_type_child_count(type_: duckdb_logical_type) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Retrieves the name of the struct child.\n\nThe result must be freed with `duckdb_free`.\n\n @param type The logical type object\n @param index The child index\n @return The name of the struct type. Must be freed with `duckdb_free`."]
    pub fn duckdb_struct_type_child_name(type_: duckdb_logical_type, index: idx_t) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Retrieves the child type of the given struct type at the specified index.\n\nThe result must be freed with `duckdb_destroy_logical_type`.\n\n @param type The logical type object\n @param index The child index\n @return The child type of the struct type. Must be destroyed with `duckdb_destroy_logical_type`."]
    pub fn duckdb_struct_type_child_type(type_: duckdb_logical_type, index: idx_t) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Returns the number of members that the union type has.\n\n @param type The logical type (union) object\n @return The number of members of a union type."]
    pub fn duckdb_union_type_member_count(type_: duckdb_logical_type) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Retrieves the name of the union member.\n\nThe result must be freed with `duckdb_free`.\n\n @param type The logical type object\n @param index The child index\n @return The name of the union member. Must be freed with `duckdb_free`."]
    pub fn duckdb_union_type_member_name(type_: duckdb_logical_type, index: idx_t) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Retrieves the child type of the given union member at the specified index.\n\nThe result must be freed with `duckdb_destroy_logical_type`.\n\n @param type The logical type object\n @param index The child index\n @return The child type of the union member. Must be destroyed with `duckdb_destroy_logical_type`."]
    pub fn duckdb_union_type_member_type(type_: duckdb_logical_type, index: idx_t) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Destroys the logical type and de-allocates all memory allocated for that type.\n\n @param type The logical type to destroy."]
    pub fn duckdb_destroy_logical_type(type_: *mut duckdb_logical_type);
}
unsafe extern "C" {
    #[doc = "Registers a custom type within the given connection.\nThe type must have an alias\n\n @param con The connection to use\n @param type The custom type to register\n @return Whether or not the registration was successful."]
    pub fn duckdb_register_logical_type(
        con: duckdb_connection,
        type_: duckdb_logical_type,
        info: duckdb_create_type_info,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Creates an empty data chunk with the specified column types.\nThe result must be destroyed with `duckdb_destroy_data_chunk`.\n\n @param types An array of column types. Column types can not contain ANY and INVALID types.\n @param column_count The number of columns.\n @return The data chunk."]
    pub fn duckdb_create_data_chunk(types: *mut duckdb_logical_type, column_count: idx_t) -> duckdb_data_chunk;
}
unsafe extern "C" {
    #[doc = "Destroys the data chunk and de-allocates all memory allocated for that chunk.\n\n @param chunk The data chunk to destroy."]
    pub fn duckdb_destroy_data_chunk(chunk: *mut duckdb_data_chunk);
}
unsafe extern "C" {
    #[doc = "Resets a data chunk, clearing the validity masks and setting the cardinality of the data chunk to 0.\nAfter calling this method, you must call `duckdb_vector_get_validity` and `duckdb_vector_get_data` to obtain current\ndata and validity pointers\n\n @param chunk The data chunk to reset."]
    pub fn duckdb_data_chunk_reset(chunk: duckdb_data_chunk);
}
unsafe extern "C" {
    #[doc = "Retrieves the number of columns in a data chunk.\n\n @param chunk The data chunk to get the data from\n @return The number of columns in the data chunk"]
    pub fn duckdb_data_chunk_get_column_count(chunk: duckdb_data_chunk) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Retrieves the vector at the specified column index in the data chunk.\n\nThe pointer to the vector is valid for as long as the chunk is alive.\nIt does NOT need to be destroyed.\n\n @param chunk The data chunk to get the data from\n @return The vector"]
    pub fn duckdb_data_chunk_get_vector(chunk: duckdb_data_chunk, col_idx: idx_t) -> duckdb_vector;
}
unsafe extern "C" {
    #[doc = "Retrieves the current number of tuples in a data chunk.\n\n @param chunk The data chunk to get the data from\n @return The number of tuples in the data chunk"]
    pub fn duckdb_data_chunk_get_size(chunk: duckdb_data_chunk) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Sets the current number of tuples in a data chunk.\n\n @param chunk The data chunk to set the size in\n @param size The number of tuples in the data chunk"]
    pub fn duckdb_data_chunk_set_size(chunk: duckdb_data_chunk, size: idx_t);
}
unsafe extern "C" {
    #[doc = "Creates a flat vector."]
    pub fn duckdb_create_vector(type_: duckdb_logical_type, capacity: idx_t) -> duckdb_vector;
}
unsafe extern "C" {
    #[doc = "Destroys the vector and de-allocates all memory allocated for that vector, if unused else where."]
    pub fn duckdb_destroy_vector(vector: *mut duckdb_vector);
}
unsafe extern "C" {
    #[doc = "Retrieves the column type of the specified vector.\n\nThe result must be destroyed with `duckdb_destroy_logical_type`.\n\n @param vector The vector get the data from\n @return The type of the vector"]
    pub fn duckdb_vector_get_column_type(vector: duckdb_vector) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Retrieves the data pointer of the vector.\n\nThe data pointer can be used to read or write values from the vector.\nHow to read or write values depends on the type of the vector.\n\n @param vector The vector to get the data from\n @return The data pointer"]
    pub fn duckdb_vector_get_data(vector: duckdb_vector) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Retrieves the validity mask pointer of the specified vector.\n\nIf all values are valid, this function MIGHT return NULL!\n\nThe validity mask is a bitset that signifies null-ness within the data chunk.\nIt is a series of uint64_t values, where each uint64_t value contains validity for 64 tuples.\nThe bit is set to 1 if the value is valid (i.e. not NULL) or 0 if the value is invalid (i.e. NULL).\n\nValidity of a specific value can be obtained like this:\n\nidx_t entry_idx = row_idx / 64;\nidx_t idx_in_entry = row_idx % 64;\nbool is_valid = validity_mask[entry_idx] & (1 << idx_in_entry);\n\nAlternatively, the (slower) duckdb_validity_row_is_valid function can be used.\n\n @param vector The vector to get the data from\n @return The pointer to the validity mask, or NULL if no validity mask is present"]
    pub fn duckdb_vector_get_validity(vector: duckdb_vector) -> *mut u64;
}
unsafe extern "C" {
    #[doc = "Ensures the validity mask is writable by allocating it.\n\nAfter this function is called, `duckdb_vector_get_validity` will ALWAYS return non-NULL.\nThis allows NULL values to be written to the vector, regardless of whether a validity mask was present before.\n\n @param vector The vector to alter"]
    pub fn duckdb_vector_ensure_validity_writable(vector: duckdb_vector);
}
unsafe extern "C" {
    #[doc = "Assigns a string element in the vector at the specified location.\n\n @param vector The vector to alter\n @param index The row position in the vector to assign the string to\n @param str The null-terminated string"]
    pub fn duckdb_vector_assign_string_element(
        vector: duckdb_vector,
        index: idx_t,
        str_: *const ::std::os::raw::c_char,
    );
}
unsafe extern "C" {
    #[doc = "Assigns a string element in the vector at the specified location. You may also use this function to assign BLOBs.\n\n @param vector The vector to alter\n @param index The row position in the vector to assign the string to\n @param str The string\n @param str_len The length of the string (in bytes)"]
    pub fn duckdb_vector_assign_string_element_len(
        vector: duckdb_vector,
        index: idx_t,
        str_: *const ::std::os::raw::c_char,
        str_len: idx_t,
    );
}
unsafe extern "C" {
    #[doc = "Retrieves the child vector of a list vector.\n\nThe resulting vector is valid as long as the parent vector is valid.\n\n @param vector The vector\n @return The child vector"]
    pub fn duckdb_list_vector_get_child(vector: duckdb_vector) -> duckdb_vector;
}
unsafe extern "C" {
    #[doc = "Returns the size of the child vector of the list.\n\n @param vector The vector\n @return The size of the child list"]
    pub fn duckdb_list_vector_get_size(vector: duckdb_vector) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Sets the total size of the underlying child-vector of a list vector.\n\n @param vector The list vector.\n @param size The size of the child list.\n @return The duckdb state. Returns DuckDBError if the vector is nullptr."]
    pub fn duckdb_list_vector_set_size(vector: duckdb_vector, size: idx_t) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Sets the total capacity of the underlying child-vector of a list.\n\nAfter calling this method, you must call `duckdb_vector_get_validity` and `duckdb_vector_get_data` to obtain current\ndata and validity pointers\n\n @param vector The list vector.\n @param required_capacity the total capacity to reserve.\n @return The duckdb state. Returns DuckDBError if the vector is nullptr."]
    pub fn duckdb_list_vector_reserve(vector: duckdb_vector, required_capacity: idx_t) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Retrieves the child vector of a struct vector.\n\nThe resulting vector is valid as long as the parent vector is valid.\n\n @param vector The vector\n @param index The child index\n @return The child vector"]
    pub fn duckdb_struct_vector_get_child(vector: duckdb_vector, index: idx_t) -> duckdb_vector;
}
unsafe extern "C" {
    #[doc = "Retrieves the child vector of an array vector.\n\nThe resulting vector is valid as long as the parent vector is valid.\nThe resulting vector has the size of the parent vector multiplied by the array size.\n\n @param vector The vector\n @return The child vector"]
    pub fn duckdb_array_vector_get_child(vector: duckdb_vector) -> duckdb_vector;
}
unsafe extern "C" {
    #[doc = "Slice a vector with a selection vector.\n\nThe max value in the selection vector must be less than the length of the vector\n\nThe resulting vector happens to be a dictionary vector.\n\n @param vector The vector which is to become a dictionary\n @param selection The selection vector\n @param len The length of the selection vector"]
    pub fn duckdb_slice_vector(vector: duckdb_vector, selection: duckdb_selection_vector, len: idx_t);
}
unsafe extern "C" {
    #[doc = "Copies the value from `value` to `vector`."]
    pub fn duckdb_vector_reference_value(vector: duckdb_vector, value: duckdb_value);
}
unsafe extern "C" {
    #[doc = "References the `from` vector in the `to` vector, this makes take shared ownership of the values buffer"]
    pub fn duckdb_vector_reference_vector(to_vector: duckdb_vector, from_vector: duckdb_vector);
}
unsafe extern "C" {
    #[doc = "Returns whether or not a row is valid (i.e. not NULL) in the given validity mask.\n\n @param validity The validity mask, as obtained through `duckdb_vector_get_validity`\n @param row The row index\n @return true if the row is valid, false otherwise"]
    pub fn duckdb_validity_row_is_valid(validity: *mut u64, row: idx_t) -> bool;
}
unsafe extern "C" {
    #[doc = "In a validity mask, sets a specific row to either valid or invalid.\n\nNote that `duckdb_vector_ensure_validity_writable` should be called before calling `duckdb_vector_get_validity`,\nto ensure that there is a validity mask to write to.\n\n @param validity The validity mask, as obtained through `duckdb_vector_get_validity`.\n @param row The row index\n @param valid Whether or not to set the row to valid, or invalid"]
    pub fn duckdb_validity_set_row_validity(validity: *mut u64, row: idx_t, valid: bool);
}
unsafe extern "C" {
    #[doc = "In a validity mask, sets a specific row to invalid.\n\nEquivalent to `duckdb_validity_set_row_validity` with valid set to false.\n\n @param validity The validity mask\n @param row The row index"]
    pub fn duckdb_validity_set_row_invalid(validity: *mut u64, row: idx_t);
}
unsafe extern "C" {
    #[doc = "In a validity mask, sets a specific row to valid.\n\nEquivalent to `duckdb_validity_set_row_validity` with valid set to true.\n\n @param validity The validity mask\n @param row The row index"]
    pub fn duckdb_validity_set_row_valid(validity: *mut u64, row: idx_t);
}
unsafe extern "C" {
    #[doc = "Creates a new empty scalar function.\n\nThe return value must be destroyed with `duckdb_destroy_scalar_function`.\n\n @return The scalar function object."]
    pub fn duckdb_create_scalar_function() -> duckdb_scalar_function;
}
unsafe extern "C" {
    #[doc = "Destroys the given scalar function object.\n\n @param scalar_function The scalar function to destroy"]
    pub fn duckdb_destroy_scalar_function(scalar_function: *mut duckdb_scalar_function);
}
unsafe extern "C" {
    #[doc = "Sets the name of the given scalar function.\n\n @param scalar_function The scalar function\n @param name The name of the scalar function"]
    pub fn duckdb_scalar_function_set_name(
        scalar_function: duckdb_scalar_function,
        name: *const ::std::os::raw::c_char,
    );
}
unsafe extern "C" {
    #[doc = "Sets the parameters of the given scalar function to varargs. Does not require adding parameters with\nduckdb_scalar_function_add_parameter.\n\n @param scalar_function The scalar function.\n @param type The type of the arguments.\n @return The parameter type. Cannot contain INVALID."]
    pub fn duckdb_scalar_function_set_varargs(scalar_function: duckdb_scalar_function, type_: duckdb_logical_type);
}
unsafe extern "C" {
    #[doc = "Sets the scalar function's null-handling behavior to special.\n\n @param scalar_function The scalar function."]
    pub fn duckdb_scalar_function_set_special_handling(scalar_function: duckdb_scalar_function);
}
unsafe extern "C" {
    #[doc = "Sets the Function Stability of the scalar function to VOLATILE, indicating the function should be re-run for every row.\nThis limits optimization that can be performed for the function.\n\n @param scalar_function The scalar function."]
    pub fn duckdb_scalar_function_set_volatile(scalar_function: duckdb_scalar_function);
}
unsafe extern "C" {
    #[doc = "Adds a parameter to the scalar function.\n\n @param scalar_function The scalar function.\n @param type The parameter type. Cannot contain INVALID."]
    pub fn duckdb_scalar_function_add_parameter(scalar_function: duckdb_scalar_function, type_: duckdb_logical_type);
}
unsafe extern "C" {
    #[doc = "Sets the return type of the scalar function.\n\n @param scalar_function The scalar function\n @param type Cannot contain INVALID or ANY."]
    pub fn duckdb_scalar_function_set_return_type(scalar_function: duckdb_scalar_function, type_: duckdb_logical_type);
}
unsafe extern "C" {
    #[doc = "Assigns extra information to the scalar function that can be fetched during binding, etc.\n\n @param scalar_function The scalar function\n @param extra_info The extra information\n @param destroy The callback that will be called to destroy the extra information (if any)"]
    pub fn duckdb_scalar_function_set_extra_info(
        scalar_function: duckdb_scalar_function,
        extra_info: *mut ::std::os::raw::c_void,
        destroy: duckdb_delete_callback_t,
    );
}
unsafe extern "C" {
    #[doc = "Sets the (optional) bind function of the scalar function.\n\n @param scalar_function The scalar function\n @param bind The bind function"]
    pub fn duckdb_scalar_function_set_bind(
        scalar_function: duckdb_scalar_function,
        bind: duckdb_scalar_function_bind_t,
    );
}
unsafe extern "C" {
    #[doc = "Sets the user-provided bind data in the bind object of the scalar function.\nThis object can be retrieved again during execution.\n\n @param info The bind info of the scalar function.\n @param bind_data The bind data object.\n @param destroy The callback to destroy the bind data (if any)."]
    pub fn duckdb_scalar_function_set_bind_data(
        info: duckdb_bind_info,
        bind_data: *mut ::std::os::raw::c_void,
        destroy: duckdb_delete_callback_t,
    );
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while calling bind on a scalar function.\n\n @param info The bind info object\n @param error The error message"]
    pub fn duckdb_scalar_function_bind_set_error(info: duckdb_bind_info, error: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Sets the main function of the scalar function.\n\n @param scalar_function The scalar function\n @param function The function"]
    pub fn duckdb_scalar_function_set_function(
        scalar_function: duckdb_scalar_function,
        function: duckdb_scalar_function_t,
    );
}
unsafe extern "C" {
    #[doc = "Register the scalar function object within the given connection.\n\nThe function requires at least a name, a function and a return type.\n\nIf the function is incomplete or a function with this name already exists DuckDBError is returned.\n\n @param con The connection to register it in.\n @param scalar_function The function pointer\n @return Whether or not the registration was successful."]
    pub fn duckdb_register_scalar_function(
        con: duckdb_connection,
        scalar_function: duckdb_scalar_function,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Retrieves the extra info of the function as set in `duckdb_scalar_function_set_extra_info`.\n\n @param info The info object.\n @return The extra info."]
    pub fn duckdb_scalar_function_get_extra_info(info: duckdb_function_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Gets the scalar function's bind data set by `duckdb_scalar_function_set_bind_data`.\n\nNote that the bind data is read-only.\n\n @param info The function info.\n @return The bind data object."]
    pub fn duckdb_scalar_function_get_bind_data(info: duckdb_function_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Retrieves the client context of the bind info of a scalar function.\n\n @param info The bind info object of the scalar function.\n @param out_context The client context of the bind info. Must be destroyed with `duckdb_destroy_client_context`."]
    pub fn duckdb_scalar_function_get_client_context(info: duckdb_bind_info, out_context: *mut duckdb_client_context);
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while executing the scalar function.\n\n @param info The info object.\n @param error The error message"]
    pub fn duckdb_scalar_function_set_error(info: duckdb_function_info, error: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Creates a new empty scalar function set.\n\nThe return value must be destroyed with `duckdb_destroy_scalar_function_set`.\n\n @return The scalar function set object."]
    pub fn duckdb_create_scalar_function_set(name: *const ::std::os::raw::c_char) -> duckdb_scalar_function_set;
}
unsafe extern "C" {
    #[doc = "Destroys the given scalar function set object."]
    pub fn duckdb_destroy_scalar_function_set(scalar_function_set: *mut duckdb_scalar_function_set);
}
unsafe extern "C" {
    #[doc = "Adds the scalar function as a new overload to the scalar function set.\n\nReturns DuckDBError if the function could not be added, for example if the overload already exists.\n\n @param set The scalar function set\n @param function The function to add"]
    pub fn duckdb_add_scalar_function_to_set(
        set: duckdb_scalar_function_set,
        function: duckdb_scalar_function,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Register the scalar function set within the given connection.\n\nThe set requires at least a single valid overload.\n\nIf the set is incomplete or a function with this name already exists DuckDBError is returned.\n\n @param con The connection to register it in.\n @param set The function set to register\n @return Whether or not the registration was successful."]
    pub fn duckdb_register_scalar_function_set(con: duckdb_connection, set: duckdb_scalar_function_set)
        -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Creates a new selection vector of size `size`."]
    pub fn duckdb_create_selection_vector(size: idx_t) -> duckdb_selection_vector;
}
unsafe extern "C" {
    #[doc = "Destroys a selection vector."]
    pub fn duckdb_destroy_selection_vector(vector: duckdb_selection_vector);
}
unsafe extern "C" {
    #[doc = "Access the data pointer of a selection vector."]
    pub fn duckdb_selection_vector_get_data_ptr(vector: duckdb_selection_vector) -> *mut sel_t;
}
unsafe extern "C" {
    #[doc = "Creates a new empty aggregate function.\n\nThe return value should be destroyed with `duckdb_destroy_aggregate_function`.\n\n @return The aggregate function object."]
    pub fn duckdb_create_aggregate_function() -> duckdb_aggregate_function;
}
unsafe extern "C" {
    #[doc = "Destroys the given aggregate function object."]
    pub fn duckdb_destroy_aggregate_function(aggregate_function: *mut duckdb_aggregate_function);
}
unsafe extern "C" {
    #[doc = "Sets the name of the given aggregate function.\n\n @param aggregate_function The aggregate function\n @param name The name of the aggregate function"]
    pub fn duckdb_aggregate_function_set_name(
        aggregate_function: duckdb_aggregate_function,
        name: *const ::std::os::raw::c_char,
    );
}
unsafe extern "C" {
    #[doc = "Adds a parameter to the aggregate function.\n\n @param aggregate_function The aggregate function.\n @param type The parameter type. Cannot contain INVALID."]
    pub fn duckdb_aggregate_function_add_parameter(
        aggregate_function: duckdb_aggregate_function,
        type_: duckdb_logical_type,
    );
}
unsafe extern "C" {
    #[doc = "Sets the return type of the aggregate function.\n\n @param aggregate_function The aggregate function.\n @param type The return type. Cannot contain INVALID or ANY."]
    pub fn duckdb_aggregate_function_set_return_type(
        aggregate_function: duckdb_aggregate_function,
        type_: duckdb_logical_type,
    );
}
unsafe extern "C" {
    #[doc = "Sets the main functions of the aggregate function.\n\n @param aggregate_function The aggregate function\n @param state_size state size\n @param state_init state init function\n @param update update states\n @param combine combine states\n @param finalize finalize states"]
    pub fn duckdb_aggregate_function_set_functions(
        aggregate_function: duckdb_aggregate_function,
        state_size: duckdb_aggregate_state_size,
        state_init: duckdb_aggregate_init_t,
        update: duckdb_aggregate_update_t,
        combine: duckdb_aggregate_combine_t,
        finalize: duckdb_aggregate_finalize_t,
    );
}
unsafe extern "C" {
    #[doc = "Sets the state destructor callback of the aggregate function (optional)\n\n @param aggregate_function The aggregate function\n @param destroy state destroy callback"]
    pub fn duckdb_aggregate_function_set_destructor(
        aggregate_function: duckdb_aggregate_function,
        destroy: duckdb_aggregate_destroy_t,
    );
}
unsafe extern "C" {
    #[doc = "Register the aggregate function object within the given connection.\n\nThe function requires at least a name, functions and a return type.\n\nIf the function is incomplete or a function with this name already exists DuckDBError is returned.\n\n @param con The connection to register it in.\n @return Whether or not the registration was successful."]
    pub fn duckdb_register_aggregate_function(
        con: duckdb_connection,
        aggregate_function: duckdb_aggregate_function,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Sets the NULL handling of the aggregate function to SPECIAL_HANDLING.\n\n @param aggregate_function The aggregate function"]
    pub fn duckdb_aggregate_function_set_special_handling(aggregate_function: duckdb_aggregate_function);
}
unsafe extern "C" {
    #[doc = "Assigns extra information to the scalar function that can be fetched during binding, etc.\n\n @param aggregate_function The aggregate function\n @param extra_info The extra information\n @param destroy The callback that will be called to destroy the extra information (if any)"]
    pub fn duckdb_aggregate_function_set_extra_info(
        aggregate_function: duckdb_aggregate_function,
        extra_info: *mut ::std::os::raw::c_void,
        destroy: duckdb_delete_callback_t,
    );
}
unsafe extern "C" {
    #[doc = "Retrieves the extra info of the function as set in `duckdb_aggregate_function_set_extra_info`.\n\n @param info The info object\n @return The extra info"]
    pub fn duckdb_aggregate_function_get_extra_info(info: duckdb_function_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while executing the aggregate function.\n\n @param info The info object\n @param error The error message"]
    pub fn duckdb_aggregate_function_set_error(info: duckdb_function_info, error: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Creates a new empty aggregate function set.\n\nThe return value should be destroyed with `duckdb_destroy_aggregate_function_set`.\n\n @return The aggregate function set object."]
    pub fn duckdb_create_aggregate_function_set(name: *const ::std::os::raw::c_char) -> duckdb_aggregate_function_set;
}
unsafe extern "C" {
    #[doc = "Destroys the given aggregate function set object."]
    pub fn duckdb_destroy_aggregate_function_set(aggregate_function_set: *mut duckdb_aggregate_function_set);
}
unsafe extern "C" {
    #[doc = "Adds the aggregate function as a new overload to the aggregate function set.\n\nReturns DuckDBError if the function could not be added, for example if the overload already exists.\n\n @param set The aggregate function set\n @param function The function to add"]
    pub fn duckdb_add_aggregate_function_to_set(
        set: duckdb_aggregate_function_set,
        function: duckdb_aggregate_function,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Register the aggregate function set within the given connection.\n\nThe set requires at least a single valid overload.\n\nIf the set is incomplete or a function with this name already exists DuckDBError is returned.\n\n @param con The connection to register it in.\n @param set The function set to register\n @return Whether or not the registration was successful."]
    pub fn duckdb_register_aggregate_function_set(
        con: duckdb_connection,
        set: duckdb_aggregate_function_set,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Creates a new empty table function.\n\nThe return value should be destroyed with `duckdb_destroy_table_function`.\n\n @return The table function object."]
    pub fn duckdb_create_table_function() -> duckdb_table_function;
}
unsafe extern "C" {
    #[doc = "Destroys the given table function object.\n\n @param table_function The table function to destroy"]
    pub fn duckdb_destroy_table_function(table_function: *mut duckdb_table_function);
}
unsafe extern "C" {
    #[doc = "Sets the name of the given table function.\n\n @param table_function The table function\n @param name The name of the table function"]
    pub fn duckdb_table_function_set_name(table_function: duckdb_table_function, name: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Adds a parameter to the table function.\n\n @param table_function The table function.\n @param type The parameter type. Cannot contain INVALID."]
    pub fn duckdb_table_function_add_parameter(table_function: duckdb_table_function, type_: duckdb_logical_type);
}
unsafe extern "C" {
    #[doc = "Adds a named parameter to the table function.\n\n @param table_function The table function.\n @param name The parameter name.\n @param type The parameter type. Cannot contain INVALID."]
    pub fn duckdb_table_function_add_named_parameter(
        table_function: duckdb_table_function,
        name: *const ::std::os::raw::c_char,
        type_: duckdb_logical_type,
    );
}
unsafe extern "C" {
    #[doc = "Assigns extra information to the table function that can be fetched during binding, etc.\n\n @param table_function The table function\n @param extra_info The extra information\n @param destroy The callback that will be called to destroy the extra information (if any)"]
    pub fn duckdb_table_function_set_extra_info(
        table_function: duckdb_table_function,
        extra_info: *mut ::std::os::raw::c_void,
        destroy: duckdb_delete_callback_t,
    );
}
unsafe extern "C" {
    #[doc = "Sets the bind function of the table function.\n\n @param table_function The table function\n @param bind The bind function"]
    pub fn duckdb_table_function_set_bind(table_function: duckdb_table_function, bind: duckdb_table_function_bind_t);
}
unsafe extern "C" {
    #[doc = "Sets the init function of the table function.\n\n @param table_function The table function\n @param init The init function"]
    pub fn duckdb_table_function_set_init(table_function: duckdb_table_function, init: duckdb_table_function_init_t);
}
unsafe extern "C" {
    #[doc = "Sets the thread-local init function of the table function.\n\n @param table_function The table function\n @param init The init function"]
    pub fn duckdb_table_function_set_local_init(
        table_function: duckdb_table_function,
        init: duckdb_table_function_init_t,
    );
}
unsafe extern "C" {
    #[doc = "Sets the main function of the table function.\n\n @param table_function The table function\n @param function The function"]
    pub fn duckdb_table_function_set_function(table_function: duckdb_table_function, function: duckdb_table_function_t);
}
unsafe extern "C" {
    #[doc = "Sets whether or not the given table function supports projection pushdown.\n\nIf this is set to true, the system will provide a list of all required columns in the `init` stage through\nthe `duckdb_init_get_column_count` and `duckdb_init_get_column_index` functions.\nIf this is set to false (the default), the system will expect all columns to be projected.\n\n @param table_function The table function\n @param pushdown True if the table function supports projection pushdown, false otherwise."]
    pub fn duckdb_table_function_supports_projection_pushdown(table_function: duckdb_table_function, pushdown: bool);
}
unsafe extern "C" {
    #[doc = "Register the table function object within the given connection.\n\nThe function requires at least a name, a bind function, an init function and a main function.\n\nIf the function is incomplete or a function with this name already exists DuckDBError is returned.\n\n @param con The connection to register it in.\n @param function The function pointer\n @return Whether or not the registration was successful."]
    pub fn duckdb_register_table_function(con: duckdb_connection, function: duckdb_table_function) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Retrieves the extra info of the function as set in `duckdb_table_function_set_extra_info`.\n\n @param info The info object\n @return The extra info"]
    pub fn duckdb_bind_get_extra_info(info: duckdb_bind_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Adds a result column to the output of the table function.\n\n @param info The table function's bind info.\n @param name The column name.\n @param type The logical column type."]
    pub fn duckdb_bind_add_result_column(
        info: duckdb_bind_info,
        name: *const ::std::os::raw::c_char,
        type_: duckdb_logical_type,
    );
}
unsafe extern "C" {
    #[doc = "Retrieves the number of regular (non-named) parameters to the function.\n\n @param info The info object\n @return The number of parameters"]
    pub fn duckdb_bind_get_parameter_count(info: duckdb_bind_info) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Retrieves the parameter at the given index.\n\nThe result must be destroyed with `duckdb_destroy_value`.\n\n @param info The info object\n @param index The index of the parameter to get\n @return The value of the parameter. Must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_bind_get_parameter(info: duckdb_bind_info, index: idx_t) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Retrieves a named parameter with the given name.\n\nThe result must be destroyed with `duckdb_destroy_value`.\n\n @param info The info object\n @param name The name of the parameter\n @return The value of the parameter. Must be destroyed with `duckdb_destroy_value`."]
    pub fn duckdb_bind_get_named_parameter(info: duckdb_bind_info, name: *const ::std::os::raw::c_char)
        -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Sets the user-provided bind data in the bind object of the table function.\nThis object can be retrieved again during execution.\n\n @param info The bind info of the table function.\n @param bind_data The bind data object.\n @param destroy The callback to destroy the bind data (if any)."]
    pub fn duckdb_bind_set_bind_data(
        info: duckdb_bind_info,
        bind_data: *mut ::std::os::raw::c_void,
        destroy: duckdb_delete_callback_t,
    );
}
unsafe extern "C" {
    #[doc = "Sets the cardinality estimate for the table function, used for optimization.\n\n @param info The bind data object.\n @param is_exact Whether or not the cardinality estimate is exact, or an approximation"]
    pub fn duckdb_bind_set_cardinality(info: duckdb_bind_info, cardinality: idx_t, is_exact: bool);
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while calling bind on a table function.\n\n @param info The info object\n @param error The error message"]
    pub fn duckdb_bind_set_error(info: duckdb_bind_info, error: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Retrieves the extra info of the function as set in `duckdb_table_function_set_extra_info`.\n\n @param info The info object\n @return The extra info"]
    pub fn duckdb_init_get_extra_info(info: duckdb_init_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Gets the bind data set by `duckdb_bind_set_bind_data` during the bind.\n\nNote that the bind data should be considered as read-only.\nFor tracking state, use the init data instead.\n\n @param info The info object\n @return The bind data object"]
    pub fn duckdb_init_get_bind_data(info: duckdb_init_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Sets the user-provided init data in the init object. This object can be retrieved again during execution.\n\n @param info The info object\n @param init_data The init data object.\n @param destroy The callback that will be called to destroy the init data (if any)"]
    pub fn duckdb_init_set_init_data(
        info: duckdb_init_info,
        init_data: *mut ::std::os::raw::c_void,
        destroy: duckdb_delete_callback_t,
    );
}
unsafe extern "C" {
    #[doc = "Returns the number of projected columns.\n\nThis function must be used if projection pushdown is enabled to figure out which columns to emit.\n\n @param info The info object\n @return The number of projected columns."]
    pub fn duckdb_init_get_column_count(info: duckdb_init_info) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Returns the column index of the projected column at the specified position.\n\nThis function must be used if projection pushdown is enabled to figure out which columns to emit.\n\n @param info The info object\n @param column_index The index at which to get the projected column index, from 0..duckdb_init_get_column_count(info)\n @return The column index of the projected column."]
    pub fn duckdb_init_get_column_index(info: duckdb_init_info, column_index: idx_t) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Sets how many threads can process this table function in parallel (default: 1)\n\n @param info The info object\n @param max_threads The maximum amount of threads that can process this table function"]
    pub fn duckdb_init_set_max_threads(info: duckdb_init_info, max_threads: idx_t);
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while calling init.\n\n @param info The info object\n @param error The error message"]
    pub fn duckdb_init_set_error(info: duckdb_init_info, error: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Retrieves the extra info of the function as set in `duckdb_table_function_set_extra_info`.\n\n @param info The info object\n @return The extra info"]
    pub fn duckdb_function_get_extra_info(info: duckdb_function_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Gets the table function's bind data set by `duckdb_bind_set_bind_data`.\n\nNote that the bind data is read-only.\nFor tracking state, use the init data instead.\n\n @param info The function info object.\n @return The bind data object."]
    pub fn duckdb_function_get_bind_data(info: duckdb_function_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Gets the init data set by `duckdb_init_set_init_data` during the init.\n\n @param info The info object\n @return The init data object"]
    pub fn duckdb_function_get_init_data(info: duckdb_function_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Gets the thread-local init data set by `duckdb_init_set_init_data` during the local_init.\n\n @param info The info object\n @return The init data object"]
    pub fn duckdb_function_get_local_init_data(info: duckdb_function_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while executing the function.\n\n @param info The info object\n @param error The error message"]
    pub fn duckdb_function_set_error(info: duckdb_function_info, error: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Add a replacement scan definition to the specified database.\n\n @param db The database object to add the replacement scan to\n @param replacement The replacement scan callback\n @param extra_data Extra data that is passed back into the specified callback\n @param delete_callback The delete callback to call on the extra data, if any"]
    pub fn duckdb_add_replacement_scan(
        db: duckdb_database,
        replacement: duckdb_replacement_callback_t,
        extra_data: *mut ::std::os::raw::c_void,
        delete_callback: duckdb_delete_callback_t,
    );
}
unsafe extern "C" {
    #[doc = "Sets the replacement function name. If this function is called in the replacement callback,\nthe replacement scan is performed. If it is not called, the replacement callback is not performed.\n\n @param info The info object\n @param function_name The function name to substitute."]
    pub fn duckdb_replacement_scan_set_function_name(
        info: duckdb_replacement_scan_info,
        function_name: *const ::std::os::raw::c_char,
    );
}
unsafe extern "C" {
    #[doc = "Adds a parameter to the replacement scan function.\n\n @param info The info object\n @param parameter The parameter to add."]
    pub fn duckdb_replacement_scan_add_parameter(info: duckdb_replacement_scan_info, parameter: duckdb_value);
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while executing the replacement scan.\n\n @param info The info object\n @param error The error message"]
    pub fn duckdb_replacement_scan_set_error(info: duckdb_replacement_scan_info, error: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Returns the root node of the profiling information. Returns nullptr, if profiling is not enabled.\n\n @param connection A connection object.\n @return A profiling information object."]
    pub fn duckdb_get_profiling_info(connection: duckdb_connection) -> duckdb_profiling_info;
}
unsafe extern "C" {
    #[doc = "Returns the value of the metric of the current profiling info node. Returns nullptr, if the metric does\nnot exist or is not enabled. Currently, the value holds a string, and you can retrieve the string\nby calling the corresponding function: char *duckdb_get_varchar(duckdb_value value).\n\n @param info A profiling information object.\n @param key The name of the requested metric.\n @return The value of the metric. Must be freed with `duckdb_destroy_value`"]
    pub fn duckdb_profiling_info_get_value(
        info: duckdb_profiling_info,
        key: *const ::std::os::raw::c_char,
    ) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns the key-value metric map of this profiling node as a MAP duckdb_value.\nThe individual elements are accessible via the duckdb_value MAP functions.\n\n @param info A profiling information object.\n @return The key-value metric map as a MAP duckdb_value."]
    pub fn duckdb_profiling_info_get_metrics(info: duckdb_profiling_info) -> duckdb_value;
}
unsafe extern "C" {
    #[doc = "Returns the number of children in the current profiling info node.\n\n @param info A profiling information object.\n @return The number of children in the current node."]
    pub fn duckdb_profiling_info_get_child_count(info: duckdb_profiling_info) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Returns the child node at the specified index.\n\n @param info A profiling information object.\n @param index The index of the child node.\n @return The child node at the specified index."]
    pub fn duckdb_profiling_info_get_child(info: duckdb_profiling_info, index: idx_t) -> duckdb_profiling_info;
}
unsafe extern "C" {
    #[doc = "Creates an appender object.\n\nNote that the object must be destroyed with `duckdb_appender_destroy`.\n\n @param connection The connection context to create the appender in.\n @param schema The schema of the table to append to, or `nullptr` for the default schema.\n @param table The table name to append to.\n @param out_appender The resulting appender object.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_appender_create(
        connection: duckdb_connection,
        schema: *const ::std::os::raw::c_char,
        table: *const ::std::os::raw::c_char,
        out_appender: *mut duckdb_appender,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Creates an appender object.\n\nNote that the object must be destroyed with `duckdb_appender_destroy`.\n\n @param connection The connection context to create the appender in.\n @param catalog The catalog of the table to append to, or `nullptr` for the default catalog.\n @param schema The schema of the table to append to, or `nullptr` for the default schema.\n @param table The table name to append to.\n @param out_appender The resulting appender object.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_appender_create_ext(
        connection: duckdb_connection,
        catalog: *const ::std::os::raw::c_char,
        schema: *const ::std::os::raw::c_char,
        table: *const ::std::os::raw::c_char,
        out_appender: *mut duckdb_appender,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Returns the number of columns that belong to the appender.\nIf there is no active column list, then this equals the table's physical columns.\n\n @param appender The appender to get the column count from.\n @return The number of columns in the data chunks."]
    pub fn duckdb_appender_column_count(appender: duckdb_appender) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Returns the type of the column at the specified index. This is either a type in the active column list, or the same type\nas a column in the receiving table.\n\nNote: The resulting type must be destroyed with `duckdb_destroy_logical_type`.\n\n @param appender The appender to get the column type from.\n @param col_idx The index of the column to get the type of.\n @return The `duckdb_logical_type` of the column."]
    pub fn duckdb_appender_column_type(appender: duckdb_appender, col_idx: idx_t) -> duckdb_logical_type;
}
unsafe extern "C" {
    #[doc = "Returns the error message associated with the given appender.\nIf the appender has no error message, this returns `nullptr` instead.\n\nThe error message should not be freed. It will be de-allocated when `duckdb_appender_destroy` is called.\n\n @param appender The appender to get the error from.\n @return The error message, or `nullptr` if there is none."]
    pub fn duckdb_appender_error(appender: duckdb_appender) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Flush the appender to the table, forcing the cache of the appender to be cleared. If flushing the data triggers a\nconstraint violation or any other error, then all data is invalidated, and this function returns DuckDBError.\nIt is not possible to append more values. Call duckdb_appender_error to obtain the error message followed by\nduckdb_appender_destroy to destroy the invalidated appender.\n\n @param appender The appender to flush.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_appender_flush(appender: duckdb_appender) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Closes the appender by flushing all intermediate states and closing it for further appends. If flushing the data\ntriggers a constraint violation or any other error, then all data is invalidated, and this function returns DuckDBError.\nCall duckdb_appender_error to obtain the error message followed by duckdb_appender_destroy to destroy the invalidated\nappender.\n\n @param appender The appender to flush and close.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_appender_close(appender: duckdb_appender) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Closes the appender by flushing all intermediate states to the table and destroying it. By destroying it, this function\nde-allocates all memory associated with the appender. If flushing the data triggers a constraint violation,\nthen all data is invalidated, and this function returns DuckDBError. Due to the destruction of the appender, it is no\nlonger possible to obtain the specific error message with duckdb_appender_error. Therefore, call duckdb_appender_close\nbefore destroying the appender, if you need insights into the specific error.\n\n @param appender The appender to flush, close and destroy.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_appender_destroy(appender: *mut duckdb_appender) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Appends a column to the active column list of the appender. Immediately flushes all previous data.\n\nThe active column list specifies all columns that are expected when flushing the data. Any non-active columns are filled\nwith their default values, or NULL.\n\n @param appender The appender to add the column to.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_appender_add_column(appender: duckdb_appender, name: *const ::std::os::raw::c_char) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Removes all columns from the active column list of the appender, resetting the appender to treat all columns as active.\nImmediately flushes all previous data.\n\n @param appender The appender to clear the columns from.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_appender_clear_columns(appender: duckdb_appender) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "A nop function, provided for backwards compatibility reasons. Does nothing. Only `duckdb_appender_end_row` is required."]
    pub fn duckdb_appender_begin_row(appender: duckdb_appender) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Finish the current row of appends. After end_row is called, the next row can be appended.\n\n @param appender The appender.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_appender_end_row(appender: duckdb_appender) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a DEFAULT value (NULL if DEFAULT not available for column) to the appender."]
    pub fn duckdb_append_default(appender: duckdb_appender) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a DEFAULT value, at the specified row and column, (NULL if DEFAULT not available for column) to the chunk created\nfrom the specified appender. The default value of the column must be a constant value. Non-deterministic expressions\nlike nextval('seq') or random() are not supported.\n\n @param appender The appender to get the default value from.\n @param chunk The data chunk to append the default value to.\n @param col The chunk column index to append the default value to.\n @param row The chunk row index to append the default value to.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_append_default_to_chunk(
        appender: duckdb_appender,
        chunk: duckdb_data_chunk,
        col: idx_t,
        row: idx_t,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a bool value to the appender."]
    pub fn duckdb_append_bool(appender: duckdb_appender, value: bool) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append an int8_t value to the appender."]
    pub fn duckdb_append_int8(appender: duckdb_appender, value: i8) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append an int16_t value to the appender."]
    pub fn duckdb_append_int16(appender: duckdb_appender, value: i16) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append an int32_t value to the appender."]
    pub fn duckdb_append_int32(appender: duckdb_appender, value: i32) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append an int64_t value to the appender."]
    pub fn duckdb_append_int64(appender: duckdb_appender, value: i64) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a duckdb_hugeint value to the appender."]
    pub fn duckdb_append_hugeint(appender: duckdb_appender, value: duckdb_hugeint) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a uint8_t value to the appender."]
    pub fn duckdb_append_uint8(appender: duckdb_appender, value: u8) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a uint16_t value to the appender."]
    pub fn duckdb_append_uint16(appender: duckdb_appender, value: u16) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a uint32_t value to the appender."]
    pub fn duckdb_append_uint32(appender: duckdb_appender, value: u32) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a uint64_t value to the appender."]
    pub fn duckdb_append_uint64(appender: duckdb_appender, value: u64) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a duckdb_uhugeint value to the appender."]
    pub fn duckdb_append_uhugeint(appender: duckdb_appender, value: duckdb_uhugeint) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a float value to the appender."]
    pub fn duckdb_append_float(appender: duckdb_appender, value: f32) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a double value to the appender."]
    pub fn duckdb_append_double(appender: duckdb_appender, value: f64) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a duckdb_date value to the appender."]
    pub fn duckdb_append_date(appender: duckdb_appender, value: duckdb_date) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a duckdb_time value to the appender."]
    pub fn duckdb_append_time(appender: duckdb_appender, value: duckdb_time) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a duckdb_timestamp value to the appender."]
    pub fn duckdb_append_timestamp(appender: duckdb_appender, value: duckdb_timestamp) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a duckdb_interval value to the appender."]
    pub fn duckdb_append_interval(appender: duckdb_appender, value: duckdb_interval) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a varchar value to the appender."]
    pub fn duckdb_append_varchar(appender: duckdb_appender, val: *const ::std::os::raw::c_char) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a varchar value to the appender."]
    pub fn duckdb_append_varchar_length(
        appender: duckdb_appender,
        val: *const ::std::os::raw::c_char,
        length: idx_t,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a blob value to the appender."]
    pub fn duckdb_append_blob(
        appender: duckdb_appender,
        data: *const ::std::os::raw::c_void,
        length: idx_t,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a NULL value to the appender (of any type)."]
    pub fn duckdb_append_null(appender: duckdb_appender) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Append a duckdb_value to the appender."]
    pub fn duckdb_append_value(appender: duckdb_appender, value: duckdb_value) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Appends a pre-filled data chunk to the specified appender.\nAttempts casting, if the data chunk types do not match the active appender types.\n\n @param appender The appender to append to.\n @param chunk The data chunk to append.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_append_data_chunk(appender: duckdb_appender, chunk: duckdb_data_chunk) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Creates a table description object. Note that `duckdb_table_description_destroy` should always be called on the\nresulting table_description, even if the function returns `DuckDBError`.\n\n @param connection The connection context.\n @param schema The schema of the table, or `nullptr` for the default schema.\n @param table The table name.\n @param out The resulting table description object.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_table_description_create(
        connection: duckdb_connection,
        schema: *const ::std::os::raw::c_char,
        table: *const ::std::os::raw::c_char,
        out: *mut duckdb_table_description,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Creates a table description object. Note that `duckdb_table_description_destroy` must be called on the resulting\ntable_description, even if the function returns `DuckDBError`.\n\n @param connection The connection context.\n @param catalog The catalog (database) name of the table, or `nullptr` for the default catalog.\n @param schema The schema of the table, or `nullptr` for the default schema.\n @param table The table name.\n @param out The resulting table description object.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_table_description_create_ext(
        connection: duckdb_connection,
        catalog: *const ::std::os::raw::c_char,
        schema: *const ::std::os::raw::c_char,
        table: *const ::std::os::raw::c_char,
        out: *mut duckdb_table_description,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Destroy the TableDescription object.\n\n @param table_description The table_description to destroy."]
    pub fn duckdb_table_description_destroy(table_description: *mut duckdb_table_description);
}
unsafe extern "C" {
    #[doc = "Returns the error message associated with the given table_description.\nIf the table_description has no error message, this returns `nullptr` instead.\nThe error message should not be freed. It will be de-allocated when `duckdb_table_description_destroy` is called.\n\n @param table_description The table_description to get the error from.\n @return The error message, or `nullptr` if there is none."]
    pub fn duckdb_table_description_error(table_description: duckdb_table_description)
        -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "Check if the column at 'index' index of the table has a DEFAULT expression.\n\n @param table_description The table_description to query.\n @param index The index of the column to query.\n @param out The out-parameter used to store the result.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_column_has_default(
        table_description: duckdb_table_description,
        index: idx_t,
        out: *mut bool,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Obtain the column name at 'index'.\nThe out result must be destroyed with `duckdb_free`.\n\n @param table_description The table_description to query.\n @param index The index of the column to query.\n @return The column name."]
    pub fn duckdb_table_description_get_column_name(
        table_description: duckdb_table_description,
        index: idx_t,
    ) -> *mut ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nExecutes a SQL query within a connection and stores the full (materialized) result in an arrow structure.\nIf the query fails to execute, DuckDBError is returned and the error message can be retrieved by calling\n`duckdb_query_arrow_error`.\n\nNote that after running `duckdb_query_arrow`, `duckdb_destroy_arrow` must be called on the result object even if the\nquery fails, otherwise the error stored within the result will not be freed correctly.\n\n @param connection The connection to perform the query in.\n @param query The SQL query to run.\n @param out_result The query result.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_query_arrow(
        connection: duckdb_connection,
        query: *const ::std::os::raw::c_char,
        out_result: *mut duckdb_arrow,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nFetch the internal arrow schema from the arrow result. Remember to call release on the respective\nArrowSchema object.\n\n @param result The result to fetch the schema from.\n @param out_schema The output schema.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_query_arrow_schema(result: duckdb_arrow, out_schema: *mut duckdb_arrow_schema) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nFetch the internal arrow schema from the prepared statement. Remember to call release on the respective\nArrowSchema object.\n\n @param prepared The prepared statement to fetch the schema from.\n @param out_schema The output schema.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_prepared_arrow_schema(
        prepared: duckdb_prepared_statement,
        out_schema: *mut duckdb_arrow_schema,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nConvert a data chunk into an arrow struct array. Remember to call release on the respective\nArrowArray object.\n\n @param result The result object the data chunk have been fetched from.\n @param chunk The data chunk to convert.\n @param out_array The output array."]
    pub fn duckdb_result_arrow_array(
        result: duckdb_result,
        chunk: duckdb_data_chunk,
        out_array: *mut duckdb_arrow_array,
    );
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nFetch an internal arrow struct array from the arrow result. Remember to call release on the respective\nArrowArray object.\n\nThis function can be called multiple time to get next chunks, which will free the previous out_array.\nSo consume the out_array before calling this function again.\n\n @param result The result to fetch the array from.\n @param out_array The output array.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_query_arrow_array(result: duckdb_arrow, out_array: *mut duckdb_arrow_array) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nReturns the number of columns present in the arrow result object.\n\n @param result The result object.\n @return The number of columns present in the result object."]
    pub fn duckdb_arrow_column_count(result: duckdb_arrow) -> idx_t;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nReturns the number of rows present in the arrow result object.\n\n @param result The result object.\n @return The number of rows present in the result object."]
    pub fn duckdb_arrow_row_count(result: duckdb_arrow) -> idx_t;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nReturns the number of rows changed by the query stored in the arrow result. This is relevant only for\nINSERT/UPDATE/DELETE queries. For other queries the rows_changed will be 0.\n\n @param result The result object.\n @return The number of rows changed."]
    pub fn duckdb_arrow_rows_changed(result: duckdb_arrow) -> idx_t;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nReturns the error message contained within the result. The error is only set if `duckdb_query_arrow` returns\n`DuckDBError`.\n\nThe error message should not be freed. It will be de-allocated when `duckdb_destroy_arrow` is called.\n\n @param result The result object to fetch the error from.\n @return The error of the result."]
    pub fn duckdb_query_arrow_error(result: duckdb_arrow) -> *const ::std::os::raw::c_char;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nCloses the result and de-allocates all memory allocated for the arrow result.\n\n @param result The result to destroy."]
    pub fn duckdb_destroy_arrow(result: *mut duckdb_arrow);
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nReleases the arrow array stream and de-allocates its memory.\n\n @param stream_p The arrow array stream to destroy."]
    pub fn duckdb_destroy_arrow_stream(stream_p: *mut duckdb_arrow_stream);
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nExecutes the prepared statement with the given bound parameters, and returns an arrow query result.\nNote that after running `duckdb_execute_prepared_arrow`, `duckdb_destroy_arrow` must be called on the result object.\n\n @param prepared_statement The prepared statement to execute.\n @param out_result The query result.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_execute_prepared_arrow(
        prepared_statement: duckdb_prepared_statement,
        out_result: *mut duckdb_arrow,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nScans the Arrow stream and creates a view with the given name.\n\n @param connection The connection on which to execute the scan.\n @param table_name Name of the temporary view to create.\n @param arrow Arrow stream wrapper.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_arrow_scan(
        connection: duckdb_connection,
        table_name: *const ::std::os::raw::c_char,
        arrow: duckdb_arrow_stream,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nScans the Arrow array and creates a view with the given name.\nNote that after running `duckdb_arrow_array_scan`, `duckdb_destroy_arrow_stream` must be called on the out stream.\n\n @param connection The connection on which to execute the scan.\n @param table_name Name of the temporary view to create.\n @param arrow_schema Arrow schema wrapper.\n @param arrow_array Arrow array wrapper.\n @param out_stream Output array stream that wraps around the passed schema, for releasing/deleting once done.\n @return `DuckDBSuccess` on success or `DuckDBError` on failure."]
    pub fn duckdb_arrow_array_scan(
        connection: duckdb_connection,
        table_name: *const ::std::os::raw::c_char,
        arrow_schema: duckdb_arrow_schema,
        arrow_array: duckdb_arrow_array,
        out_stream: *mut duckdb_arrow_stream,
    ) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Execute DuckDB tasks on this thread.\n\nWill return after `max_tasks` have been executed, or if there are no more tasks present.\n\n @param database The database object to execute tasks for\n @param max_tasks The maximum amount of tasks to execute"]
    pub fn duckdb_execute_tasks(database: duckdb_database, max_tasks: idx_t);
}
unsafe extern "C" {
    #[doc = "Creates a task state that can be used with duckdb_execute_tasks_state to execute tasks until\n`duckdb_finish_execution` is called on the state.\n\n`duckdb_destroy_state` must be called on the result.\n\n @param database The database object to create the task state for\n @return The task state that can be used with duckdb_execute_tasks_state."]
    pub fn duckdb_create_task_state(database: duckdb_database) -> duckdb_task_state;
}
unsafe extern "C" {
    #[doc = "Execute DuckDB tasks on this thread.\n\nThe thread will keep on executing tasks forever, until duckdb_finish_execution is called on the state.\nMultiple threads can share the same duckdb_task_state.\n\n @param state The task state of the executor"]
    pub fn duckdb_execute_tasks_state(state: duckdb_task_state);
}
unsafe extern "C" {
    #[doc = "Execute DuckDB tasks on this thread.\n\nThe thread will keep on executing tasks until either duckdb_finish_execution is called on the state,\nmax_tasks tasks have been executed or there are no more tasks to be executed.\n\nMultiple threads can share the same duckdb_task_state.\n\n @param state The task state of the executor\n @param max_tasks The maximum amount of tasks to execute\n @return The amount of tasks that have actually been executed"]
    pub fn duckdb_execute_n_tasks_state(state: duckdb_task_state, max_tasks: idx_t) -> idx_t;
}
unsafe extern "C" {
    #[doc = "Finish execution on a specific task.\n\n @param state The task state to finish execution"]
    pub fn duckdb_finish_execution(state: duckdb_task_state);
}
unsafe extern "C" {
    #[doc = "Check if the provided duckdb_task_state has finished execution\n\n @param state The task state to inspect\n @return Whether or not duckdb_finish_execution has been called on the task state"]
    pub fn duckdb_task_state_is_finished(state: duckdb_task_state) -> bool;
}
unsafe extern "C" {
    #[doc = "Destroys the task state returned from duckdb_create_task_state.\n\nNote that this should not be called while there is an active duckdb_execute_tasks_state running\non the task state.\n\n @param state The task state to clean up"]
    pub fn duckdb_destroy_task_state(state: duckdb_task_state);
}
unsafe extern "C" {
    #[doc = "Returns true if the execution of the current query is finished.\n\n @param con The connection on which to check"]
    pub fn duckdb_execution_is_finished(con: duckdb_connection) -> bool;
}
unsafe extern "C" {
    #[doc = "DEPRECATION NOTICE**: This method is scheduled for removal in a future release.\n\nFetches a data chunk from the (streaming) duckdb_result. This function should be called repeatedly until the result is\nexhausted.\n\nThe result must be destroyed with `duckdb_destroy_data_chunk`.\n\nThis function can only be used on duckdb_results created with 'duckdb_pending_prepared_streaming'\n\nIf this function is used, none of the other result functions can be used and vice versa (i.e. this function cannot be\nmixed with the legacy result functions or the materialized result functions).\n\nIt is not known beforehand how many chunks will be returned by this result.\n\n @param result The result object to fetch the data chunk from.\n @return The resulting data chunk. Returns `NULL` if the result has an error."]
    pub fn duckdb_stream_fetch_chunk(result: duckdb_result) -> duckdb_data_chunk;
}
unsafe extern "C" {
    #[doc = "Fetches a data chunk from a duckdb_result. This function should be called repeatedly until the result is exhausted.\n\nThe result must be destroyed with `duckdb_destroy_data_chunk`.\n\nIt is not known beforehand how many chunks will be returned by this result.\n\n @param result The result object to fetch the data chunk from.\n @return The resulting data chunk. Returns `NULL` if the result has an error."]
    pub fn duckdb_fetch_chunk(result: duckdb_result) -> duckdb_data_chunk;
}
unsafe extern "C" {
    #[doc = "Creates a new cast function object.\n\n @return The cast function object."]
    pub fn duckdb_create_cast_function() -> duckdb_cast_function;
}
unsafe extern "C" {
    #[doc = "Sets the source type of the cast function.\n\n @param cast_function The cast function object.\n @param source_type The source type to set."]
    pub fn duckdb_cast_function_set_source_type(cast_function: duckdb_cast_function, source_type: duckdb_logical_type);
}
unsafe extern "C" {
    #[doc = "Sets the target type of the cast function.\n\n @param cast_function The cast function object.\n @param target_type The target type to set."]
    pub fn duckdb_cast_function_set_target_type(cast_function: duckdb_cast_function, target_type: duckdb_logical_type);
}
unsafe extern "C" {
    #[doc = "Sets the \"cost\" of implicitly casting the source type to the target type using this function.\n\n @param cast_function The cast function object.\n @param cost The cost to set."]
    pub fn duckdb_cast_function_set_implicit_cast_cost(cast_function: duckdb_cast_function, cost: i64);
}
unsafe extern "C" {
    #[doc = "Sets the actual cast function to use.\n\n @param cast_function The cast function object.\n @param function The function to set."]
    pub fn duckdb_cast_function_set_function(cast_function: duckdb_cast_function, function: duckdb_cast_function_t);
}
unsafe extern "C" {
    #[doc = "Assigns extra information to the cast function that can be fetched during execution, etc.\n\n @param extra_info The extra information\n @param destroy The callback that will be called to destroy the extra information (if any)"]
    pub fn duckdb_cast_function_set_extra_info(
        cast_function: duckdb_cast_function,
        extra_info: *mut ::std::os::raw::c_void,
        destroy: duckdb_delete_callback_t,
    );
}
unsafe extern "C" {
    #[doc = "Retrieves the extra info of the function as set in `duckdb_cast_function_set_extra_info`.\n\n @param info The info object.\n @return The extra info."]
    pub fn duckdb_cast_function_get_extra_info(info: duckdb_function_info) -> *mut ::std::os::raw::c_void;
}
unsafe extern "C" {
    #[doc = "Get the cast execution mode from the given function info.\n\n @param info The info object.\n @return The cast mode."]
    pub fn duckdb_cast_function_get_cast_mode(info: duckdb_function_info) -> duckdb_cast_mode;
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while executing the cast function.\n\n @param info The info object.\n @param error The error message."]
    pub fn duckdb_cast_function_set_error(info: duckdb_function_info, error: *const ::std::os::raw::c_char);
}
unsafe extern "C" {
    #[doc = "Report that an error has occurred while executing the cast function, setting the corresponding output row to NULL.\n\n @param info The info object.\n @param error The error message.\n @param row The index of the row within the output vector to set to NULL.\n @param output The output vector."]
    pub fn duckdb_cast_function_set_row_error(
        info: duckdb_function_info,
        error: *const ::std::os::raw::c_char,
        row: idx_t,
        output: duckdb_vector,
    );
}
unsafe extern "C" {
    #[doc = "Registers a cast function within the given connection.\n\n @param con The connection to use.\n @param cast_function The cast function to register.\n @return Whether or not the registration was successful."]
    pub fn duckdb_register_cast_function(con: duckdb_connection, cast_function: duckdb_cast_function) -> duckdb_state;
}
unsafe extern "C" {
    #[doc = "Destroys the cast function object.\n\n @param cast_function The cast function object."]
    pub fn duckdb_destroy_cast_function(cast_function: *mut duckdb_cast_function);
}
