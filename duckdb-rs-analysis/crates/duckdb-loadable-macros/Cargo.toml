[package]
name = "duckdb-loadable-macros"
version = "0.1.8"
authors = { workspace = true }
edition = { workspace = true }
license = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
keywords = { workspace = true }
readme = { workspace = true }
categories = ["external-ffi-bindings", "database"]
description = "Native bindings to the libduckdb library, C API; build loadable extensions"

[dependencies]
darling = "0.20.10"
proc-macro2 = { workspace = true }
quote = { workspace = true }
syn = { workspace = true, features = [
    "extra-traits",
    "full",
    "fold",
    "parsing",
] }

[lib]
proc-macro = true
