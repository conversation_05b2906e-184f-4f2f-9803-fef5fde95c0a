[package]
name = "duckdb"
version = "1.3.1"
authors = { workspace = true }
edition = { workspace = true }
repository = { workspace = true }
homepage = { workspace = true }
documentation = { workspace = true }
readme = { workspace = true }
keywords = { workspace = true }
license = { workspace = true }
categories = { workspace = true }
description = "Ergonomic wrapper for DuckDB"

[lib]
name = "duckdb"

[features]
default = []
bundled = ["libduckdb-sys/bundled"]
json = ["libduckdb-sys/json", "bundled"]
parquet = ["libduckdb-sys/parquet", "bundled"]
vscalar = []
vscalar-arrow = []
vtab = []
vtab-loadable = ["vtab", "duckdb-loadable-macros"]
vtab-excel = ["vtab", "calamine"]
vtab-arrow = ["vtab", "num"]
appender-arrow = ["vtab-arrow"]
vtab-full = ["vtab-excel", "vtab-arrow", "appender-arrow"]
extensions-full = ["json", "parquet", "vtab-full"]
buildtime_bindgen = ["libduckdb-sys/buildtime_bindgen"]
modern-full = ["chrono", "serde_json", "url", "r2d2", "uuid", "polars"]
polars = ["dep:polars", "dep:polars-arrow"]
# FIXME: These were added to make clippy happy: these features appear unused and should perhaps be removed
column_decltype = []
extra_check = []
# Warning: experimental feature
loadable-extension = ["libduckdb-sys/loadable-extension"]

[dependencies]
arrow = { workspace = true, features = ["prettyprint", "ffi"] }
calamine = { workspace = true, optional = true }
cast = { workspace = true, features = ["std"] }
chrono = { workspace = true, optional = true }
duckdb-loadable-macros = { workspace = true, optional = true }
fallible-iterator = { workspace = true }
fallible-streaming-iterator = { workspace = true }
hashlink = { workspace = true }
libduckdb-sys = { workspace = true }
num = { workspace = true, features = ["std"], optional = true }
num-integer = { workspace = true }
polars = { workspace = true, features = ["dtype-full"], optional = true }
polars-arrow = { workspace = true, optional = true }
r2d2 = { workspace = true, optional = true }
rust_decimal = { workspace = true }
serde_json = { workspace = true, optional = true }
smallvec = { workspace = true }
strum = { workspace = true, features = ["derive"] }
url = { workspace = true, optional = true }
uuid = { workspace = true, optional = true }

[dev-dependencies]
doc-comment = { workspace = true }
polars-core = { workspace = true }
pretty_assertions = { workspace = true }
rand = { workspace = true }
tempfile = { workspace = true }
uuid = { workspace = true, features = ["v4"] }

[package.metadata.docs.rs]
features = ["vtab-full", "modern-full", "vscalar", "vscalar-arrow"]
all-features = false
no-default-features = true
default-target = "x86_64-unknown-linux-gnu"

[package.metadata.playground]
features = []
all-features = false

[[example]]
name = "hello-ext"
crate-type = ["cdylib"]
required-features = ["vtab-loadable"]

[[example]]
name = "hello-ext-capi"
crate-type = ["cdylib"]
required-features = ["vtab-loadable", "loadable-extension"]
