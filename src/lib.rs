extern crate duckdb;
extern crate duckdb_loadable_macros;
extern crate libduckdb_sys;

use duckdb::{
    core::{DataChunkHandle, LogicalTypeHandle, LogicalTypeId, Inserter},
    vtab::{BindInfo, InitInfo, TableFunctionInfo, VTab},
    Connection, Result,
};
use duckdb_loadable_macros::duckdb_entrypoint_c_api;
use libduckdb_sys as ffi;
use std::{
    error::Error,
    ffi::CString,
    sync::atomic::{AtomicBool, Ordering},
};

// JSON Schema types
#[derive(Debug, <PERSON>lone, PartialEq)]
pub enum JsonRootType {
    SingleObject,
    ArrayOfObjects,
    ArrayOfPrimitives,
    SinglePrimitive,
}

#[derive(Debug, Clone)]
pub struct JsonSchema {
    pub root_type: JsonRootType,
    pub total_rows: usize,
    pub field_names: Vec<String>, // Ordered list of field names
}

impl JsonSchema {
    pub fn new() -> Self {
        Self {
            root_type: JsonRootType::SingleObject,
            total_rows: 0,
            field_names: Vec::new(),
        }
    }

    /// Infer schema from JSON file path
    pub fn infer_from_file(file_path: &str) -> Result<Self, Box<dyn std::error::Error>> {
        use std::fs::File;
        use std::io::BufReader;
        use struson::reader::{JsonReader, JsonStreamReader, ValueType};

        let file = File::open(file_path)?;
        let reader = BufReader::new(file);
        let mut json_reader = JsonStreamReader::new(reader);

        let mut schema = JsonSchema::new();

        // Determine root type and collect field names
        match json_reader.peek()? {
            ValueType::Array => {
                // Look inside array to determine if it's objects or primitives
                json_reader.begin_array()?;
                if json_reader.has_next()? {
                    match json_reader.peek()? {
                        ValueType::Object => {
                            schema.root_type = JsonRootType::ArrayOfObjects;
                            // Collect field names from first object
                            json_reader.begin_object()?;
                            while json_reader.has_next()? {
                                let field_name = json_reader.next_name()?.to_string();
                                if !schema.field_names.contains(&field_name) {
                                    schema.field_names.push(field_name);
                                }
                                // Skip the value
                                json_reader.skip_value()?;
                            }
                            json_reader.end_object()?;

                            // Skip remaining elements in the array
                            while json_reader.has_next()? {
                                json_reader.skip_value()?;
                            }
                        }
                        _ => {
                            schema.root_type = JsonRootType::ArrayOfPrimitives;
                            schema.field_names.push("value".to_string());

                            // Skip all elements in the array
                            while json_reader.has_next()? {
                                json_reader.skip_value()?;
                            }
                        }
                    }
                } else {
                    // Empty array - default to objects
                    schema.root_type = JsonRootType::ArrayOfObjects;
                }
                json_reader.end_array()?;
            }
            ValueType::Object => {
                schema.root_type = JsonRootType::SingleObject;
                // Collect field names from the object
                json_reader.begin_object()?;
                while json_reader.has_next()? {
                    let field_name = json_reader.next_name()?.to_string();
                    schema.field_names.push(field_name);
                    // Skip the value
                    json_reader.skip_value()?;
                }
                json_reader.end_object()?;
            }
            _ => {
                schema.root_type = JsonRootType::SinglePrimitive;
                schema.field_names.push("value".to_string());
            }
        }

        Ok(schema)
    }
}

// JSON Value types for internal representation
#[derive(Debug, Clone)]
pub enum JsonValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Array(Vec<JsonValue>),
    Object(std::collections::HashMap<String, JsonValue>),
    Null,
}

// Simple JSON parser for single values
pub fn parse_simple_json(json_str: &str) -> Result<JsonValue, Box<dyn std::error::Error>> {
    use std::io::Cursor;
    use struson::reader::{JsonReader, JsonStreamReader, ValueType};

    let cursor = Cursor::new(json_str.as_bytes());
    let mut json_reader = JsonStreamReader::new(cursor);

    parse_json_value(&mut json_reader)
}

fn parse_json_value<R: std::io::Read>(reader: &mut struson::reader::JsonStreamReader<R>) -> Result<JsonValue, Box<dyn std::error::Error>> {
    use struson::reader::{JsonReader, ValueType};

    match reader.peek()? {
        ValueType::String => {
            Ok(JsonValue::String(reader.next_string()?))
        }
        ValueType::Number => {
            let number_str = reader.next_number_as_string()?;
            if let Ok(int_val) = number_str.parse::<i64>() {
                Ok(JsonValue::Number(int_val as f64))
            } else {
                Ok(JsonValue::Number(number_str.parse::<f64>()?))
            }
        }
        ValueType::Boolean => {
            Ok(JsonValue::Boolean(reader.next_bool()?))
        }
        ValueType::Null => {
            reader.next_null()?;
            Ok(JsonValue::Null)
        }
        ValueType::Array => {
            let mut array = Vec::new();
            reader.begin_array()?;
            while reader.has_next()? {
                let value = parse_json_value(reader)?;
                array.push(value);
            }
            reader.end_array()?;
            Ok(JsonValue::Array(array))
        }
        ValueType::Object => {
            let mut object = std::collections::HashMap::new();
            reader.begin_object()?;
            while reader.has_next()? {
                let field_name = reader.next_name()?.to_string();
                let value = parse_json_value(reader)?;
                object.insert(field_name, value);
            }
            reader.end_object()?;
            Ok(JsonValue::Object(object))
        }
    }
}

#[repr(C)]
struct JsonStreamBindData {
    file_path: String,
    schema: JsonSchema,
}

#[repr(C)]
struct JsonStreamInitData {
    current_row: usize,
    finished: AtomicBool,
}

struct JsonStreamVTab;

impl VTab for JsonStreamVTab {
    type InitData = JsonStreamInitData;
    type BindData = JsonStreamBindData;

    fn bind(bind: &BindInfo) -> Result<Self::BindData, Box<dyn std::error::Error>> {
        // Get file path parameter
        let file_path = bind.get_parameter(0).to_string();

        // Perform schema inference from file_path
        let schema = JsonSchema::infer_from_file(&file_path).unwrap_or_else(|_| JsonSchema::new());

        // Add result columns based on schema
        let mut final_schema = schema.clone();
        if final_schema.field_names.is_empty() {
            // Fallback to single value column
            bind.add_result_column("value", LogicalTypeHandle::from(LogicalTypeId::Varchar));
            // Also update the schema to reflect this
            final_schema.field_names.push("value".to_string());
        } else {
            // Add columns for each field
            for field_name in &final_schema.field_names {
                bind.add_result_column(field_name, LogicalTypeHandle::from(LogicalTypeId::Varchar));
            }
        }

        Ok(JsonStreamBindData {
            file_path,
            schema: final_schema,
        })
    }

    fn init(_: &InitInfo) -> Result<Self::InitData, Box<dyn std::error::Error>> {
        Ok(JsonStreamInitData {
            current_row: 0,
            finished: AtomicBool::new(false),
        })
    }

    fn func(func: &TableFunctionInfo<Self>, output: &mut DataChunkHandle) -> Result<(), Box<dyn std::error::Error>> {
        let init_data = func.get_init_data();
        let bind_data = func.get_bind_data();
        if init_data.finished.swap(true, Ordering::Relaxed) {
            output.set_len(0);
        } else {
            // Parse JSON and write to vectors
            let rows_written = parse_and_write_json(&bind_data.file_path, &bind_data.schema, output)?;
            output.set_len(rows_written);
        }
        Ok(())
    }

    fn parameters() -> Option<Vec<LogicalTypeHandle>> {
        Some(vec![LogicalTypeHandle::from(LogicalTypeId::Varchar)])
    }
}

// Vector writing function
fn write_json_to_vectors(
    output: &mut DataChunkHandle,
    schema: &JsonSchema,
    json_value: &JsonValue,
) -> Result<(), Box<dyn std::error::Error>> {
    match json_value {
        JsonValue::Object(obj) => {
            if schema.field_names.is_empty() {
                // No specific fields, write the whole object as JSON string to the "value" column
                let vector = output.flat_vector(0);
                let json_str = format!("{:?}", obj);
                let c_str = CString::new(json_str)?;
                vector.insert(0, c_str);
            } else {
                // Write object fields to columns
                for (col_index, field_name) in schema.field_names.iter().enumerate() {
                    let vector = output.flat_vector(col_index);
                    if let Some(value) = obj.get(field_name) {
                        write_json_value_to_vector(vector, 0, value)?;
                    } else {
                        // Field not present, write null
                        let mut vector = output.flat_vector(col_index);
                        vector.set_null(0);
                    }
                }
            }
        }
        JsonValue::Array(arr) => {
            // For arrays, we'll just write the first element for now
            // TODO: Implement proper array handling with multiple rows
            if let Some(first_element) = arr.first() {
                write_json_to_vectors(output, schema, first_element)?;
            } else {
                // Empty array
                for col_index in 0..schema.field_names.len() {
                    let mut vector = output.flat_vector(col_index);
                    vector.set_null(0);
                }
            }
        }
        _ => {
            // Single primitive value
            let vector = output.flat_vector(0);
            write_json_value_to_vector(vector, 0, json_value)?;
        }
    }
    Ok(())
}

fn write_json_value_to_vector(
    vector: duckdb::core::FlatVector,
    row_index: usize,
    value: &JsonValue,
) -> Result<(), Box<dyn std::error::Error>> {
    match value {
        JsonValue::String(s) => {
            let c_str = CString::new(s.clone())?;
            vector.insert(row_index, c_str);
        }
        JsonValue::Number(n) => {
            let c_str = CString::new(n.to_string())?;
            vector.insert(row_index, c_str);
        }
        JsonValue::Boolean(b) => {
            let c_str = CString::new(b.to_string())?;
            vector.insert(row_index, c_str);
        }
        JsonValue::Null => {
            let c_str = CString::new("null")?;
            vector.insert(row_index, c_str);
        }
        JsonValue::Array(arr) => {
            // Convert array to JSON-like string
            let elements: Vec<String> = arr.iter().map(|v| json_value_to_string(v)).collect();
            let json_str = format!("[{}]", elements.join(", "));
            let c_str = CString::new(json_str)?;
            vector.insert(row_index, c_str);
        }
        JsonValue::Object(obj) => {
            // Convert object to JSON-like string
            let mut pairs: Vec<String> = obj.iter()
                .map(|(k, v)| format!("\"{}\": {}", k, json_value_to_string(v)))
                .collect();
            pairs.sort(); // For consistent ordering
            let json_str = format!("{{{}}}", pairs.join(", "));
            let c_str = CString::new(json_str)?;
            vector.insert(row_index, c_str);
        }
    }
    Ok(())
}

const EXTENSION_NAME: &str = env!("CARGO_PKG_NAME");

#[duckdb_entrypoint_c_api()]
pub unsafe fn extension_entrypoint(con: Connection) -> Result<(), Box<dyn Error>> {
    con.register_table_function::<JsonStreamVTab>(EXTENSION_NAME)
        .expect("Failed to register json_stream table function");
    Ok(())
}

// Helper function to convert JsonValue to string representation
fn json_value_to_string(value: &JsonValue) -> String {
    match value {
        JsonValue::String(s) => format!("\"{}\"", s),
        JsonValue::Number(n) => n.to_string(),
        JsonValue::Boolean(b) => b.to_string(),
        JsonValue::Null => "null".to_string(),
        JsonValue::Array(arr) => {
            let elements: Vec<String> = arr.iter().map(|v| json_value_to_string(v)).collect();
            format!("[{}]", elements.join(", "))
        }
        JsonValue::Object(obj) => {
            let mut pairs: Vec<String> = obj.iter()
                .map(|(k, v)| format!("\"{}\": {}", k, json_value_to_string(v)))
                .collect();
            pairs.sort(); // For consistent ordering
            format!("{{{}}}", pairs.join(", "))
        }
    }
}

// Main JSON parsing and vector writing function
fn parse_and_write_json(
    file_path: &str,
    schema: &JsonSchema,
    output: &mut DataChunkHandle,
) -> Result<usize, Box<dyn std::error::Error>> {
    use std::fs::File;
    use std::io::BufReader;
    use struson::reader::{JsonReader, JsonStreamReader};

    let file = File::open(file_path)?;
    let reader = BufReader::new(file);
    let mut json_reader = JsonStreamReader::new(reader);

    match schema.root_type {
        JsonRootType::SingleObject => {
            parse_single_object(&mut json_reader, schema, output)?;
            Ok(1)
        }
        JsonRootType::ArrayOfObjects => {
            parse_array_of_objects(&mut json_reader, schema, output)
        }
        JsonRootType::ArrayOfPrimitives => {
            parse_array_of_primitives(&mut json_reader, schema, output)
        }
        JsonRootType::SinglePrimitive => {
            parse_single_primitive(&mut json_reader, schema, output)?;
            Ok(1)
        }
    }
}

fn parse_single_object<R: std::io::Read>(
    reader: &mut struson::reader::JsonStreamReader<R>,
    schema: &JsonSchema,
    output: &mut DataChunkHandle,
) -> Result<(), Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    reader.begin_object()?;

    // Create a map to store field values
    let mut field_values = std::collections::HashMap::new();

    while reader.has_next()? {
        let field_name = reader.next_name()?.to_string();
        let value = parse_json_value(reader)?;
        field_values.insert(field_name, value);
    }

    reader.end_object()?;

    // Write values to vectors based on schema field order
    for (col_index, field_name) in schema.field_names.iter().enumerate() {
        let vector = output.flat_vector(col_index);
        if let Some(value) = field_values.get(field_name) {
            write_json_value_to_vector(vector, 0, value)?;
        } else {
            let mut vector = output.flat_vector(col_index);
            vector.set_null(0);
        }
    }

    Ok(())
}

fn parse_array_of_objects<R: std::io::Read>(
    reader: &mut struson::reader::JsonStreamReader<R>,
    schema: &JsonSchema,
    output: &mut DataChunkHandle,
) -> Result<usize, Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    reader.begin_array()?;
    let mut row_count = 0;

    while reader.has_next()? && row_count < 1000 { // Limit to 1000 rows per chunk
        reader.begin_object()?;

        // Create a map to store field values for this row
        let mut field_values = std::collections::HashMap::new();

        while reader.has_next()? {
            let field_name = reader.next_name()?.to_string();
            let value = parse_json_value(reader)?;
            field_values.insert(field_name, value);
        }

        reader.end_object()?;

        // Write values to vectors for this row
        for (col_index, field_name) in schema.field_names.iter().enumerate() {
            let vector = output.flat_vector(col_index);
            if let Some(value) = field_values.get(field_name) {
                write_json_value_to_vector(vector, row_count, value)?;
            } else {
                let mut vector = output.flat_vector(col_index);
                vector.set_null(row_count);
            }
        }

        row_count += 1;
    }

    reader.end_array()?;
    Ok(row_count)
}

fn parse_array_of_primitives<R: std::io::Read>(
    reader: &mut struson::reader::JsonStreamReader<R>,
    _schema: &JsonSchema,
    output: &mut DataChunkHandle,
) -> Result<usize, Box<dyn std::error::Error>> {
    use struson::reader::JsonReader;

    reader.begin_array()?;
    let mut row_count = 0;

    while reader.has_next()? && row_count < 1000 { // Limit to 1000 rows per chunk
        let value = parse_json_value(reader)?;

        // Write to the "value" column (should be the only column)
        let vector = output.flat_vector(0);
        write_json_value_to_vector(vector, row_count, &value)?;

        row_count += 1;
    }

    reader.end_array()?;
    Ok(row_count)
}

fn parse_single_primitive<R: std::io::Read>(
    reader: &mut struson::reader::JsonStreamReader<R>,
    _schema: &JsonSchema,
    output: &mut DataChunkHandle,
) -> Result<(), Box<dyn std::error::Error>> {
    let value = parse_json_value(reader)?;

    // Write to the "value" column
    let vector = output.flat_vector(0);
    write_json_value_to_vector(vector, 0, &value)?;

    Ok(())
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    fn create_test_json_file(content: &str) -> NamedTempFile {
        let mut file = NamedTempFile::new().unwrap();
        file.write_all(content.as_bytes()).unwrap();
        file.flush().unwrap();
        file
    }

    #[test]
    fn test_schema_creation() {
        let schema = JsonSchema::new();
        assert_eq!(schema.root_type, JsonRootType::SingleObject);
        assert_eq!(schema.total_rows, 0);
    }

    #[test]
    fn test_schema_inference_single_object() {
        let file = create_test_json_file(r#"{"name": "Alice", "age": 30}"#);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::SingleObject);
    }

    #[test]
    fn test_schema_inference_array_of_objects() {
        let file = create_test_json_file(r#"[{"name": "Alice"}, {"name": "Bob"}]"#);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfObjects);
    }

    #[test]
    fn test_schema_inference_array_of_primitives() {
        let file = create_test_json_file(r#"[1, 2, 3, 4]"#);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfPrimitives);
    }

    #[test]
    fn test_schema_inference_single_primitive() {
        let file = create_test_json_file(r#"42"#);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::SinglePrimitive);
    }

    #[test]
    fn test_schema_inference_empty_array() {
        let file = create_test_json_file(r#"[]"#);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfObjects); // Default for empty arrays
    }

    #[test]
    fn test_schema_field_collection() {
        let file = create_test_json_file(r#"{"name": "Alice", "age": 30, "city": "NYC"}"#);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::SingleObject);
        assert_eq!(schema.field_names.len(), 3);
        assert!(schema.field_names.contains(&"name".to_string()));
        assert!(schema.field_names.contains(&"age".to_string()));
        assert!(schema.field_names.contains(&"city".to_string()));
    }

    #[test]
    fn test_schema_array_of_objects_fields() {
        let file = create_test_json_file(r#"[{"name": "Alice", "age": 30}, {"name": "Bob", "age": 25}]"#);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfObjects);
        assert_eq!(schema.field_names.len(), 2);
        assert!(schema.field_names.contains(&"name".to_string()));
        assert!(schema.field_names.contains(&"age".to_string()));
    }

    #[test]
    fn test_schema_primitive_value_field() {
        let file = create_test_json_file(r#"[1, 2, 3]"#);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfPrimitives);
        assert_eq!(schema.field_names.len(), 1);
        assert_eq!(schema.field_names[0], "value");
    }

    #[test]
    fn test_json_value_to_string_conversion() {
        // Test string conversion
        let string_val = JsonValue::String("test".to_string());
        assert_eq!(json_value_to_string(&string_val), "\"test\"");

        // Test number conversion
        let number_val = JsonValue::Number(42.5);
        assert_eq!(json_value_to_string(&number_val), "42.5");

        // Test boolean conversion
        let bool_val = JsonValue::Boolean(true);
        assert_eq!(json_value_to_string(&bool_val), "true");

        // Test null conversion
        let null_val = JsonValue::Null;
        assert_eq!(json_value_to_string(&null_val), "null");

        // Test array conversion
        let array_val = JsonValue::Array(vec![
            JsonValue::Number(1.0),
            JsonValue::Number(2.0),
        ]);
        assert_eq!(json_value_to_string(&array_val), "[1, 2]");

        // Test object conversion
        let mut obj = std::collections::HashMap::new();
        obj.insert("key".to_string(), JsonValue::String("value".to_string()));
        let object_val = JsonValue::Object(obj);
        assert_eq!(json_value_to_string(&object_val), "{\"key\": \"value\"}");
    }

    #[test]
    fn test_deeply_nested_json() {
        let nested_json = r#"{"a": {"b": {"c": {"d": {"e": "deep"}}}}}"#;
        let file = create_test_json_file(nested_json);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::SingleObject);
        assert_eq!(schema.field_names.len(), 1);
        assert_eq!(schema.field_names[0], "a");
    }

    #[test]
    fn test_large_array_schema() {
        let large_array = (0..100)
            .map(|i| format!(r#"{{"id": {}, "name": "User{}"}},"#, i, i))
            .collect::<Vec<_>>()
            .join("");
        let json_content = format!("[{}]", large_array.trim_end_matches(','));

        let file = create_test_json_file(&json_content);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfObjects);
        assert_eq!(schema.field_names.len(), 2);
        assert!(schema.field_names.contains(&"id".to_string()));
        assert!(schema.field_names.contains(&"name".to_string()));
    }

    #[test]
    fn test_mixed_array_types() {
        let mixed_json = r#"[{"type": "string", "value": "text"}, {"type": "number", "value": 42}]"#;
        let file = create_test_json_file(mixed_json);
        let schema = JsonSchema::infer_from_file(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfObjects);
        assert_eq!(schema.field_names.len(), 2);
        assert!(schema.field_names.contains(&"type".to_string()));
        assert!(schema.field_names.contains(&"value".to_string()));
    }

    #[test]
    fn test_memory_efficiency_structures() {
        // Test that our core structures are memory efficient
        use std::mem::size_of;

        // JsonValue should be reasonably sized (enum with largest variant)
        assert!(size_of::<JsonValue>() < 128, "JsonValue too large: {} bytes", size_of::<JsonValue>());

        // JsonSchema should be compact
        assert!(size_of::<JsonSchema>() < 128, "JsonSchema too large: {} bytes", size_of::<JsonSchema>());

        // JsonRootType should be tiny (enum)
        assert!(size_of::<JsonRootType>() <= 8, "JsonRootType too large: {} bytes", size_of::<JsonRootType>());
    }
}