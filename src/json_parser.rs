use struson::reader::{<PERSON><PERSON><PERSON><PERSON><PERSON>, JsonStreamReader};
use crate::schema::{JsonSchema, JsonRootType};
use crate::vector_writer::{JsonValue, JsonRowData};
use std::fs::File;
use std::io::BufReader;

/// Streaming JSON parser that integrates with struson
pub struct JsonStreamingParser {
    schema: JsonSchema,
    file_path: String,
}

impl JsonStreamingParser {
    pub fn new(file_path: String, schema: JsonSchema) -> Self {
        Self { file_path, schema }
    }

    /// Parse JSON and yield rows according to the schema
    pub fn parse_rows(&self) -> Result<JsonRowIterator<'_>, Box<dyn std::error::Error>> {
        let file = File::open(&self.file_path)?;
        let reader = BufReader::new(file);
        let json_reader = JsonStreamReader::new(reader);

        Ok(JsonRowIterator::new(json_reader, &self.schema))
    }

    /// Perform schema inference by scanning the JSON file
    pub fn infer_schema(file_path: &str) -> Result<JsonSchema, Box<dyn std::error::Error>> {
        let file = File::open(file_path)?;
        let reader = BufReader::new(file);
        let mut json_reader = JsonStreamReader::new(reader);

        // For now, implement basic schema inference
        // TODO: Improve this to properly detect root type and scan structure
        let mut schema = JsonSchema::new();

        // Try to determine root type by reading the first character
        match json_reader.peek()? {
            struson::reader::ValueType::Array => {
                schema.root_type = JsonRootType::ArrayOfObjects; // Default assumption
            }
            struson::reader::ValueType::Object => {
                schema.root_type = JsonRootType::SingleObject;
            }
            _ => {
                schema.root_type = JsonRootType::SinglePrimitive;
            }
        }

        Ok(schema)
    }
}

/// Iterator that yields JSON rows according to the schema
pub struct JsonRowIterator<'a> {
    reader: JsonStreamReader<BufReader<File>>,
    schema: &'a JsonSchema,
    current_row: usize,
    finished: bool,
}

impl<'a> JsonRowIterator<'a> {
    fn new(reader: JsonStreamReader<BufReader<File>>, schema: &'a JsonSchema) -> Self {
        Self {
            reader,
            schema,
            current_row: 0,
            finished: false,
        }
    }

    /// Parse the next row according to the schema root type
    pub fn next_row(&mut self) -> Result<Option<JsonRowData>, Box<dyn std::error::Error>> {
        if self.finished {
            return Ok(None);
        }

        match self.schema.root_type {
            JsonRootType::SingleObject => self.parse_single_object(),
            JsonRootType::ArrayOfObjects => self.parse_array_of_objects(),
            JsonRootType::ArrayOfPrimitives => self.parse_array_of_primitives(),
            JsonRootType::SinglePrimitive => self.parse_single_primitive(),
        }
    }

    fn parse_single_object(&mut self) -> Result<Option<JsonRowData>, Box<dyn std::error::Error>> {
        if self.current_row > 0 {
            self.finished = true;
            return Ok(None);
        }

        let row_data = self.parse_object_value()?;
        self.current_row += 1;
        Ok(Some(row_data))
    }

    fn parse_array_of_objects(&mut self) -> Result<Option<JsonRowData>, Box<dyn std::error::Error>> {
        // TODO: Implement array of objects parsing
        // This should:
        // 1. Begin array if first row
        // 2. Check if has_next()
        // 3. Parse next object
        // 4. Return row data
        Err("Array of objects parsing not yet implemented".into())
    }

    fn parse_array_of_primitives(&mut self) -> Result<Option<JsonRowData>, Box<dyn std::error::Error>> {
        // TODO: Implement array of primitives parsing
        // This should:
        // 1. Begin array if first row
        // 2. Check if has_next()
        // 3. Parse next primitive value
        // 4. Create row with "value" column
        Err("Array of primitives parsing not yet implemented".into())
    }

    fn parse_single_primitive(&mut self) -> Result<Option<JsonRowData>, Box<dyn std::error::Error>> {
        if self.current_row > 0 {
            self.finished = true;
            return Ok(None);
        }

        let mut row_data = JsonRowData::new();
        let value = self.parse_json_value()?;
        row_data.add_field("value".to_string(), value);
        
        self.current_row += 1;
        Ok(Some(row_data))
    }

    fn parse_object_value(&mut self) -> Result<JsonRowData, Box<dyn std::error::Error>> {
        let mut row_data = JsonRowData::new();
        
        self.reader.begin_object()?;
        while self.reader.has_next()? {
            let field_name = self.reader.next_name()?.to_string();
            let value = self.parse_json_value()?;
            row_data.add_field(field_name, value);
        }
        self.reader.end_object()?;
        
        Ok(row_data)
    }

    fn parse_json_value(&mut self) -> Result<JsonValue, Box<dyn std::error::Error>> {
        match self.reader.peek()? {
            struson::reader::ValueType::String => {
                Ok(JsonValue::String(self.reader.next_string()?))
            }
            struson::reader::ValueType::Number => {
                // Try to parse as integer first, then float
                let number_str = self.reader.next_number_as_string()?;
                if let Ok(int_val) = number_str.parse::<i64>() {
                    Ok(JsonValue::Number(int_val as f64))
                } else {
                    Ok(JsonValue::Number(number_str.parse::<f64>()?))
                }
            }
            struson::reader::ValueType::Boolean => {
                Ok(JsonValue::Boolean(self.reader.next_bool()?))
            }
            struson::reader::ValueType::Null => {
                self.reader.next_null()?;
                Ok(JsonValue::Null)
            }
            struson::reader::ValueType::Array => {
                self.parse_array_value()
            }
            struson::reader::ValueType::Object => {
                self.parse_nested_object_value()
            }
        }
    }

    fn parse_array_value(&mut self) -> Result<JsonValue, Box<dyn std::error::Error>> {
        let mut array = Vec::new();
        
        self.reader.begin_array()?;
        while self.reader.has_next()? {
            let value = self.parse_json_value()?;
            array.push(value);
        }
        self.reader.end_array()?;
        
        Ok(JsonValue::Array(array))
    }

    fn parse_nested_object_value(&mut self) -> Result<JsonValue, Box<dyn std::error::Error>> {
        let mut object = std::collections::HashMap::new();
        
        self.reader.begin_object()?;
        while self.reader.has_next()? {
            let field_name = self.reader.next_name()?.to_string();
            let value = self.parse_json_value()?;
            object.insert(field_name, value);
        }
        self.reader.end_object()?;
        
        Ok(JsonValue::Object(object))
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    fn create_test_json_file(content: &str) -> NamedTempFile {
        let mut file = NamedTempFile::new().unwrap();
        file.write_all(content.as_bytes()).unwrap();
        file.flush().unwrap();
        file
    }

    #[test]
    fn test_schema_inference_single_object() {
        let file = create_test_json_file(r#"{"name": "Alice", "age": 30}"#);
        let schema = JsonStreamingParser::infer_schema(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::SingleObject);
    }

    #[test]
    fn test_schema_inference_array_of_objects() {
        let file = create_test_json_file(r#"[{"name": "Alice"}, {"name": "Bob"}]"#);
        let schema = JsonStreamingParser::infer_schema(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfObjects);
    }

    #[test]
    fn test_schema_inference_array_of_primitives() {
        let file = create_test_json_file(r#"[1, 2, 3, 4]"#);
        let schema = JsonStreamingParser::infer_schema(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::ArrayOfPrimitives);
    }

    #[test]
    fn test_schema_inference_single_primitive() {
        let file = create_test_json_file(r#"42"#);
        let schema = JsonStreamingParser::infer_schema(file.path().to_str().unwrap()).unwrap();
        assert_eq!(schema.root_type, JsonRootType::SinglePrimitive);
    }
}
