use duckdb::core::{<PERSON><PERSON>hunk<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use crate::schema::{JsonSchema, SchemaField};
use std::ffi::CString;

/// <PERSON>les writing JSON values to DuckDB vectors
pub struct VectorWriter<'a> {
    schema: &'a JsonSchema,
}

impl<'a> VectorWriter<'a> {
    pub fn new(schema: &'a JsonSchema) -> Self {
        Self { schema }
    }

    /// Write a complete row to the output chunk
    pub fn write_row(
        &self,
        output: &mut DataChunkHandle,
        row_index: usize,
        values: &JsonRowData,
    ) -> Result<(), Box<dyn std::error::Error>> {
        for (col_index, field) in self.schema.fields.iter().enumerate() {
            self.write_field_value(output, col_index, row_index, field, values)?;
        }
        Ok(())
    }

    /// Write a single field value to the appropriate vector
    fn write_field_value(
        &self,
        output: &mut DataChunkHandle,
        col_index: usize,
        row_index: usize,
        field: &SchemaField,
        values: &JsonRowData,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let value = values.get_field(&field.name);
        
        match value {
            Some(JsonValue::String(s)) => {
                let vector = output.flat_vector(col_index);
                let c_str = CString::new(s.clone())?;
                vector.insert(row_index, c_str);
            }
            Some(JsonValue::Number(n)) => {
                let vector = output.flat_vector(col_index);
                // Convert number to string for now - TODO: Handle different numeric types based on schema
                let c_str = CString::new(n.to_string())?;
                vector.insert(row_index, c_str);
            }
            Some(JsonValue::Boolean(b)) => {
                let vector = output.flat_vector(col_index);
                let c_str = CString::new(b.to_string())?;
                vector.insert(row_index, c_str);
            }
            Some(JsonValue::Array(arr)) => {
                self.write_array_value(output, col_index, row_index, field, arr)?;
            }
            Some(JsonValue::Object(obj)) => {
                self.write_struct_value(output, col_index, row_index, field, obj)?;
            }
            Some(JsonValue::Null) | None => {
                // Handle null values
                let mut vector = output.flat_vector(col_index);
                vector.set_null(row_index);
            }
        }
        
        Ok(())
    }

    /// Write array values to ListVector
    fn write_array_value(
        &self,
        _output: &mut DataChunkHandle,
        _col_index: usize,
        _row_index: usize,
        _field: &SchemaField,
        _array: &[JsonValue],
    ) -> Result<(), Box<dyn std::error::Error>> {
        let _list_vector = _output.list_vector(_col_index);
        // TODO: Implement array writing with proper offset management
        // This requires:
        // 1. Calculate cumulative offsets
        // 2. Write array elements to child vector
        // 3. Set list entry with offset and length
        Err("Array writing not yet implemented".into())
    }

    /// Write struct values to StructVector
    fn write_struct_value(
        &self,
        _output: &mut DataChunkHandle,
        _col_index: usize,
        _row_index: usize,
        field: &SchemaField,
        _object: &std::collections::HashMap<String, JsonValue>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        let _struct_vector = _output.struct_vector(_col_index);
        
        if let Some(children) = &field.children {
            // TODO: Implement struct writing
            // This requires:
            // 1. Get child vectors from struct vector
            // 2. Write each field to appropriate child vector
            // 3. Handle nested structures recursively
            for _child in children {
                // Write child field values
            }
        }
        
        Err("Struct writing not yet implemented".into())
    }
}

/// Represents a JSON value in our internal format
#[derive(Debug, Clone)]
pub enum JsonValue {
    String(String),
    Number(f64),
    Boolean(bool),
    Array(Vec<JsonValue>),
    Object(std::collections::HashMap<String, JsonValue>),
    Null,
}

/// Represents a complete row of JSON data
#[derive(Debug, Clone)]
pub struct JsonRowData {
    fields: std::collections::HashMap<String, JsonValue>,
}

impl JsonRowData {
    pub fn new() -> Self {
        Self {
            fields: std::collections::HashMap::new(),
        }
    }

    pub fn add_field(&mut self, name: String, value: JsonValue) {
        self.fields.insert(name, value);
    }

    pub fn get_field(&self, name: &str) -> Option<&JsonValue> {
        self.fields.get(name)
    }
}

/// Manages offset tracking for LIST vectors
pub struct OffsetManager {
    cumulative_offsets: Vec<usize>,
    current_offset: usize,
}

impl OffsetManager {
    pub fn new() -> Self {
        Self {
            cumulative_offsets: vec![0],
            current_offset: 0,
        }
    }

    /// Add an array of given length and return the start offset
    pub fn add_array(&mut self, length: usize) -> usize {
        let start_offset = self.current_offset;
        self.current_offset += length;
        self.cumulative_offsets.push(self.current_offset);
        start_offset
    }

    /// Get the offset for a specific row
    pub fn get_offset(&self, row: usize) -> usize {
        self.cumulative_offsets[row]
    }

    /// Get the length for a specific row
    pub fn get_length(&self, row: usize) -> usize {
        if row + 1 < self.cumulative_offsets.len() {
            self.cumulative_offsets[row + 1] - self.cumulative_offsets[row]
        } else {
            0
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_json_row_data() {
        let mut row = JsonRowData::new();
        row.add_field("name".to_string(), JsonValue::String("Alice".to_string()));
        row.add_field("age".to_string(), JsonValue::Number(30.0));
        
        assert!(matches!(row.get_field("name"), Some(JsonValue::String(_))));
        assert!(matches!(row.get_field("age"), Some(JsonValue::Number(_))));
        assert!(row.get_field("nonexistent").is_none());
    }

    #[test]
    fn test_offset_manager() {
        let mut manager = OffsetManager::new();
        
        // Add first array of length 3
        let offset1 = manager.add_array(3);
        assert_eq!(offset1, 0);
        assert_eq!(manager.get_length(0), 3);
        
        // Add second array of length 2
        let offset2 = manager.add_array(2);
        assert_eq!(offset2, 3);
        assert_eq!(manager.get_length(1), 2);
        
        // Check cumulative offsets
        assert_eq!(manager.get_offset(0), 0);
        assert_eq!(manager.get_offset(1), 3);
        assert_eq!(manager.get_offset(2), 5);
    }

    #[test]
    fn test_json_value_types() {
        let string_val = JsonValue::String("test".to_string());
        let number_val = JsonValue::Number(42.0);
        let bool_val = JsonValue::Boolean(true);
        let null_val = JsonValue::Null;
        
        assert!(matches!(string_val, JsonValue::String(_)));
        assert!(matches!(number_val, JsonValue::Number(_)));
        assert!(matches!(bool_val, JsonValue::Boolean(_)));
        assert!(matches!(null_val, JsonValue::Null));
    }
}
